<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){
	
	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';	
	openConnection();
	
	if ($_POST['oper']=='edit')	{
		
		$id = $_POST['id'];
		$books = implode(',',$_POST['books']);

		$SQL = "UPDATE ebook_teacher SET ";
		$SQL .= "books='$books' ";
		$SQL .= " WHERE teacher_id=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

	}
	
	closeConnection();
	
}

echo json_encode($data);

?>
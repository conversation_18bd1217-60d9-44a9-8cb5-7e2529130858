<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] != 1 ) {
		header("Location: /ebookshelf/logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){

	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';
	openConnection();

	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$name = $objConn->real_escape_string($_POST['student_name']);
		$pwd = $objConn->real_escape_string($_POST['pwd']);
		$classid = $objConn->real_escape_string($_POST['class_id']);

		// get last student id
		$SQL2 = "SELECT student_id FROM student WHERE school_code='".$_SESSION['school']."' ORDER BY student_id DESC LIMIT 1 ";
		if ($result = $objConn->query($SQL2)) {
			$lastID = '';
			while($row = $result->fetch_assoc()) {
				$lastID = $row['student_id'];
			}
			if ( $lastID == '' ) {
				$currentID = 0;
			} else {
				$currentID = intval( str_replace($_SESSION['school'],"",$lastID) );
			}
			$studentID = $_SESSION['school'] . str_pad( $currentID + 1 ,5,"0",STR_PAD_LEFT);
			$SQL3 = "INSERT INTO student ";
			$SQL3 .= "(student_id,student_name,pwd,class_id,teacher_id,school_code,wrong_count,deleted) VALUES ";
			$SQL3 .= "('$studentID','$name','$pwd','$classid','".$_SESSION['user_session']."','".$_SESSION['school']."','0','0')";
			if ($objConn->query($SQL3) === TRUE) {
				$data->success = true;
				$data->err = '';
			} else {
				$data->success = false;
				$data->err = $objConn->error;
			}
			
			// log ?
		}


	} elseif ($_POST['oper']=='edit')	{

		$id = $_POST['id'];
		$name = $objConn->real_escape_string($_POST['student_name']);
		$pwd = $objConn->real_escape_string($_POST['pwd']);

		$SQL = "UPDATE student SET ";
		$SQL .= "student_name='$name',";
		$SQL .= "pwd='$pwd' ";
		$SQL .= " WHERE student_id='$id' ";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}
		
		// log ?

	} elseif ($_POST['oper']=='del')	{

		$id = $_POST['id'];
		$SQL = "DELETE FROM student WHERE student_id='$id' ";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
			
			// del also from student_bookmark, student_custom, student_result
			$SQL = "DELETE FROM student_bookmark WHERE student_id='$id' ";
			$objConn->query($SQL);
			
			$SQL = "DELETE FROM student_custom WHERE student_id='$id' ";
			$objConn->query($SQL);
			
			$SQL = "DELETE FROM student_log WHERE student_id='$id' ";
			$objConn->query($SQL);
			
			$SQL = "DELETE FROM student_log WHERE student_id='$id' ";
			$objConn->query($SQL);
			
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

		// log ?
		
	}

	closeConnection();

}

echo json_encode($data);

function randomPassword() {
    $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
    $pass = array(); //remember to declare $pass as an array
    $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
    for ($i = 0; $i < 8; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass); //turn the array into a string
}
?>
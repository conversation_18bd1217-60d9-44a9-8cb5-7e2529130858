<?php 
error_reporting(E_ALL);
ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

// $bookid = isset($_POST["book"]) ? $_POST["book"] : 0;

require_once '../../inc/settings.inc.php';
require_once '../../inc/functions.inc.php';
openConnection();

// $condition = "deleted=0 AND book='$bookid' ";
$condition = " 1 ";
$responce = new stdClass();

if (isset($_POST['_search'])) {

	if ($_POST['_search']<>'false'){

	$searchField = $_POST['searchField'];
	$searchOper = $_POST['searchOper'];
	$searchString = $_POST['searchString'];
	switch ($searchOper) {
		case "eq":
			$condition .= " AND ".$searchField; 
			$condition .= " =";
			$condition .= " '".$searchString."'";
			break;
		case "ne":
			$condition .= " AND ".$searchField; 
			$condition .= " <>";
			$condition .= " '".$searchString."'";
			break;
		case "bw":
			$condition .= " AND ".$searchField; 
			$condition .= " LIKE";
			$condition .= " '".$searchString."%'";
			break;
		case "bn":
			$condition .= " AND ".$searchField; 
			$condition .= " NOT LIKE";
			$condition .= " '".$searchString."%'";
			break;
		case "ew":
			$condition .= " AND ".$searchField; 
			$condition .= " LIKE";
			$condition .= " '%".$searchString."'";
			break;
		case "en":
			$condition .= " AND ".$searchField; 
			$condition .= " NOT LIKE";
			$condition .= " '%".$searchString."'";
			break;
		case "cn":
			$condition .= " AND ".$searchField; 
			$condition .= " LIKE";
			$condition .= " '%".$searchString."%'";
			break;
		case "nc":
			$condition .= " AND ".$searchField; 
			$condition .= " NOT LIKE";
			$condition .= " '%".$searchString."%'";
			break;
		case "nu":
			$condition .= " AND ".$searchField; 
			$condition .= " IS NULL";
			break;
		case "nn":
			$condition .= " AND ".$searchField; 
			$condition .= " IS NOT NULL";
			break;
		case "in":
			$condition .= " AND ".$searchField; 
			$condition .= " IN";
			$condition .= " ('".$searchString."')";
			break;
		case "ni":
			$condition .= " AND ".$searchField; 
			$condition .= " NOT IN";
			$condition .= " ('".$searchString."')";
			break;
		case "lt":
			$condition .= " AND ".$searchField; 
			$condition .= " <";
			$condition .= " ('".$searchString."')";
			break;
		case "le":
			$condition .= " AND ".$searchField; 
			$condition .= " <=";
			$condition .= " ('".$searchString."')";
			break;
		case "gt":
			$condition .= " AND ".$searchField; 
			$condition .= " >";
			$condition .= " ('".$searchString."')";
			break;
		case "ge":
			$condition .= " AND ".$searchField; 
			$condition .= " >=";
			$condition .= " ('".$searchString."')";
			break;
	}	
	
	}
}
//echo $search;
//echo $condition;

$page = $_POST['page']; // get the requested page
$limit = $_POST['rows']; // get how many rows we want to have into the grid
$sidx = $_POST['sidx']; // get index row - i.e. user click to sort
$sord = $_POST['sord']; // get the direction
if(!$sidx) $sidx =1;

if ($result = $objConn->query("SELECT count(*) as count FROM teacher WHERE $condition ")) {
	$row = $result->fetch_array();
	$count = $row['count'];
	$result->close();
} else {
	$count = 0;
}

// for group by only
// $sql = "SELECT ind FROM asset WHERE $condition GROUP BY ind";
// if ($result = $objConn->query($sql)) {
	// $count = $result->num_rows;
	// $result->close();
// } else {
	// $count = 0;
// }

$total_pages = ceil($count/$limit);

if ($count>0) {
	if ($page > $total_pages) $page=$total_pages;
	$start = $limit*$page - $limit; // do not put $limit*($page - 1)
	if ($start<0) $start=0;
	// $SQL = "SELECT * FROM ebook_teacher WHERE $condition ORDER BY $sidx $sord LIMIT $start , $limit"; 
	// $SQL = "SELECT ebook_teacher.*, group_concat(ebook_book_user.book_id) as books FROM ebook_teacher, ebook_book_user WHERE ebook_book_user.teacher_id=ebook_teacher.teacher_id GROUP BY teacher_id ORDER BY $sidx $sord LIMIT $start , $limit";
	$SQL = "SELECT * FROM teacher WHERE $condition ORDER BY $sidx $sord LIMIT $start , $limit";
	if ($result = $objConn->query($SQL)) {
		$i=0;
		while($row = $result->fetch_array()) {
			$responce->rows[$i]=$row;
			$i++;
		}
		$result->close();
	}
}

closeConnection();

$responce->page = $page;
$responce->total = $total_pages;
$responce->records = $count;

echo json_encode($responce);

?>
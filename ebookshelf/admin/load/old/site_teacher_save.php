<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){
	
	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';	
	openConnection();
	
	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$teaLogin = $objConn->real_escape_string($_POST['teaLogin']);
		$teaPw = $objConn->real_escape_string($_POST['teaPw']);
		$teaName = $objConn->real_escape_string($_POST['teaName']);
		$schId = $objConn->real_escape_string($_POST['schId']);
		$teaExpire = $objConn->real_escape_string($_POST['teaExpire']);
	
		$SQL = "INSERT INTO teacher ";
		$SQL .= "(teaLogin,teaPw,teaName,schId,teaExpire) VALUES ";
		$SQL .= "('$teaLogin','$teaPw','$teaName','$schId','$teaExpire')";
		if ($objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $objConn->insert_id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

		
	} elseif ($_POST['oper']=='edit')	{
		
	
		$id = $_POST['id'];
		$teaLogin = $objConn->real_escape_string($_POST['teaLogin']);
		$teaPw = $objConn->real_escape_string($_POST['teaPw']);
		$teaName = $objConn->real_escape_string($_POST['teaName']);
		$schId = $objConn->real_escape_string($_POST['schId']);
		$teaExpire = $objConn->real_escape_string($_POST['teaExpire']);

		$SQL = "UPDATE teacher SET ";
		$SQL .= "teaLogin='$teaLogin',";
		$SQL .= "teaPw='$teaPw',";
		$SQL .= "teaName='$teaName',";
		$SQL .= "schId='$schId',";
		$SQL .= "teaExpire='$teaExpire' ";
		$SQL .= " WHERE teaId=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

	}
	
	closeConnection();
	
}

echo json_encode($data);

?>
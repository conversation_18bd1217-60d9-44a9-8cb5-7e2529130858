<?php 
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

// $bookid = isset($_POST["book"]) ? $_POST["book"] : 0;

require_once '../../inc/settings.inc.php';
require_once '../../inc/functions.inc.php';
openConnection();

$responce = '';


$SQL = "SELECT * FROM school WHERE 1 ORDER BY schId ASC"; 
if ($result = $objConn->query($SQL)) {
	$i=0;
	while($row = $result->fetch_array()) {
		$responce .= $row['schId'] .':'. $row['schCName'] . ';';
		$i++;
	}
	$result->close();
}

$responce = trim($responce, ";");

// $responce .= '</select>';
closeConnection();

echo $responce;
?>
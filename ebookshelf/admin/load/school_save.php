<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){
	
	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';	
	openConnection();
	
	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$schCode = $objConn->real_escape_string($_POST['schCode']);
		$schCName = $objConn->real_escape_string($_POST['schCName']);
		$schEName = $objConn->real_escape_string($_POST['schEName']);
		$schCAdd = $objConn->real_escape_string($_POST['schCAdd']);
	
		$SQL = "INSERT INTO school ";
		$SQL .= "(schCode,schEName,schCName,schCAdd,schLang) VALUES ";
		$SQL .= "('$schCode','$schEName','$schCName','$schCAdd','0')";
		if ($objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $objConn->insert_id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

		
	} elseif ($_POST['oper']=='edit')	{
		
	
		$id = $_POST['id'];
		$schCode = $objConn->real_escape_string($_POST['schCode']);
		$schCName = $objConn->real_escape_string($_POST['schCName']);
		$schEName = $objConn->real_escape_string($_POST['schEName']);
		$schCAdd = $objConn->real_escape_string($_POST['schCAdd']);

		$SQL = "UPDATE school SET ";
		$SQL .= "schCode='$schCode',";
		$SQL .= "schCName='$schCName',";
		$SQL .= "schEName='$schEName',";
		$SQL .= "schCAdd='$schCAdd' "; 		
		$SQL .= " WHERE schId=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

	}
	
	closeConnection();
	
}

echo json_encode($data);

?>
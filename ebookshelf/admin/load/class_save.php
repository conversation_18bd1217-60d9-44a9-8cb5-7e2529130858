<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] != 1 ) {
		header("Location: /ebookshelf/logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){

	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';
	openConnection();

	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$name = $objConn->real_escape_string($_POST['class_name']);
		$size = $_POST['student_count'];
		$tid = $_POST['teacher_id'];
		// $books = implode(',',$_POST['books']);
		
		$getSchCode = "SELECT school.schCode FROM ebook_teacher, school WHERE ebook_teacher.school_id=school.schId AND ebook_teacher.teacher_id=$tid ";
		if ($result = $objConn->query($getSchCode)) {
			while($row = $result->fetch_assoc()) {
				$school_code = $row['schCode'];
			}
		}
			
		$SQL = "INSERT INTO student_class ";
		$SQL .= "(class_name,size,teacher_id,school_code,deleted,edit_date) VALUES ";
		$SQL .= "('$name','$size','$tid','$school_code','0','".date("Y-m-d H:i:s")."')";
		if ($objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $objConn->insert_id;

			// get last student id
			$SQL2 = "SELECT student_id FROM student WHERE student_id LIKE '$school_code%' ORDER BY student_id DESC LIMIT 1 ";
			if ($result = $objConn->query($SQL2)) {
				$lastID = '';
				while($row = $result->fetch_assoc()) {
					$lastID = $row['student_id'];
				}
				if ( $lastID == '' ) {
					$currentID = 0;
				} else {
					$currentID = intval( str_replace($school_code,"",$lastID) );
				}
				for ($x = 1; $x <= $size; $x++) {                                         
					$studentID = $school_code . str_pad( $currentID + $x ,5,"0",STR_PAD_LEFT);
					$pwd = randomPassword();
					$SQL3 = "INSERT INTO student ";
					$SQL3 .= "(student_id,student_name,pwd,class_id,teacher_id,school_code,wrong_count,deleted) VALUES ";
					$SQL3 .= "('$studentID','$studentID','$pwd','".$data->new_id."','$tid','$school_code','0','0')";
					if ($objConn->query($SQL3) === TRUE) {
						$data->success = true;
						$data->err = '';
					} else {
						$data->success = false;
						$data->err = $objConn->error;
					}
				}
			}

		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}


	} elseif ($_POST['oper']=='edit')	{

		$id = $_POST['id'];
		// $tid = $_POST['teacher_id'];
		$name = $objConn->real_escape_string($_POST['class_name']);
		// $books = implode(',',$_POST['books']);

		$SQL = "UPDATE student_class SET ";
		$SQL .= "class_name='$name',";
		$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
		$SQL .= " WHERE class_id=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}
	} elseif ($_POST['oper']=='del')	{
		
		$id = $_POST['id'];
		$SQL = "UPDATE student_class SET deleted=1 WHERE class_id=$id";
		
		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}
		
	}

	closeConnection();

}

echo json_encode($data);

function randomPassword() {
    $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
    $pass = array(); //remember to declare $pass as an array
    $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
    for ($i = 0; $i < 8; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass); //turn the array into a string
}
?>
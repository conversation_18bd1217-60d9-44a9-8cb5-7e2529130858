<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){
	
	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';	
	openConnection();
	
	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$bookPath = $objConn->real_escape_string($_POST['book_path']);
		$bookTitle = $objConn->real_escape_string($_POST['book_title']);
		$bookPic = $objConn->real_escape_string($_POST['book_pic']);
		$disabled = $objConn->real_escape_string($_POST['disabled']);
	
		$SQL = "INSERT INTO ebook_book ";
		$SQL .= "(book_path,book_title,book_pic,disabled,edit_user,edit_date) VALUES ";
		$SQL .= "('$bookPath','$bookTitle','$bookPic','$disabled','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
		if ($objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $objConn->insert_id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

		
	} elseif ($_POST['oper']=='edit')	{
		
	
		$id = $_POST['id'];
		$bookPath = $objConn->real_escape_string($_POST['book_path']);
		$bookTitle = $objConn->real_escape_string($_POST['book_title']);
		$bookPic = $objConn->real_escape_string($_POST['book_pic']);
		$disabled = $objConn->real_escape_string($_POST['disabled']);

		$SQL = "UPDATE ebook_book SET ";
		$SQL .= "book_path='$bookPath',";
		$SQL .= "book_title='$bookTitle',";
		$SQL .= "book_pic='$bookPic',";
		$SQL .= "disabled='$disabled',";
		$SQL .= "edit_user='".$_SESSION['user_session']."',";
		$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";		
		$SQL .= " WHERE book_id=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->sql = $SQL;
			$data->new_id = 0;
		}

	}
	
	closeConnection();
	
}

echo json_encode($data);

?>
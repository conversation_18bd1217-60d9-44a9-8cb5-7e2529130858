<?php 
error_reporting(E_ALL);
ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] != 1 ) {
		header("Location: /ebookshelf/logout.php");
	}
}

// $bookid = isset($_POST["book"]) ? $_POST["book"] : 0;

require_once '../../inc/settings.inc.php';
require_once '../../inc/functions.inc.php';
openConnection();

$responce = new stdClass();


$SQL = "SELECT * FROM ebook_book WHERE 1 ORDER BY book_id ASC"; 
if ($result = $objConn->query($SQL)) {
	$i=0;
	while($row = $result->fetch_array()) {
		$responce->rows[$i]=$row;
		$i++;
	}
	$result->close();
}


// $responce .= '</select>';
closeConnection();

echo json_encode($responce);
?>
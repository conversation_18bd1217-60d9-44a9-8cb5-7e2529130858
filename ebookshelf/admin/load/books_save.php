<?php
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

session_start();
if( !isset($_SESSION['user_session']) ) {
	header("Location: ../login.php");
} else {
	if ( $_SESSION['user_session'] != 1 ) {
		header("Location: ../logout.php");
	}
}

$data = new stdClass;
$data->success = false;
$data->err = '系統錯誤。';

if ( isset($_POST['oper']) ){
	
	require_once '../../inc/settings.inc.php';
	require_once '../../inc/functions.inc.php';	
	openConnection();
	
	if ($_POST['oper']=='add')	{

		// $id = $_POST['id'];
		$teacher_id = $objConn->real_escape_string($_POST['teacher_id']);
		$book_id = $objConn->real_escape_string($_POST['book_id']);
		$disabled = $objConn->real_escape_string($_POST['disabled']);
	
		$SQL = "INSERT INTO ebook_book_user ";
		$SQL .= "(teacher_id,book_id,disabled,edit_user,edit_date) VALUES ";
		$SQL .= "('$teacher_id','$book_id','$disabled','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
		if ($objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $objConn->insert_id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

		
	} elseif ($_POST['oper']=='edit')	{
		
	
		$id = $_POST['id'];
		// $teacher_id = $objConn->real_escape_string($_POST['teacher_id']);
		$book_id = $objConn->real_escape_string($_POST['book_id']);
		$disabled = $objConn->real_escape_string($_POST['disabled']);

		$SQL = "UPDATE ebook_book_user SET ";
		$SQL .= "book_id='$book_id',";
		$SQL .= "disabled='$disabled',";
		$SQL .= "edit_user='".$_SESSION['user_session']."',";
		$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
		$SQL .= " WHERE id=$id";

		if ( $objConn->query($SQL) === TRUE) {
			$data->success = true;
			$data->err = '';
			$data->new_id = $id;
		} else {
			$data->success = false;
			$data->err = $objConn->error;
			$data->new_id = 0;
		}

	}
	
	closeConnection();
	
}

echo json_encode($data);

?>
<?php
session_start();
if( !isset($_SESSION['user_session']) || $_SESSION['user_session'] != 1 ) {
	header("Location: ../login.php");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
	<title>eBookShelf Manager</title>
	<meta http-equiv="Content-type" content="text/html;charset=UTF-8" />

	<link rel="stylesheet" type="text/css" media="screen" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" media="screen" href="css/ui.jqgrid.css" />

	<script src="js/jquery-1.11.0.min.js" type="text/javascript"></script>
	<script src="js/jquery-ui.min.js"></script>
	<script src="js/grid.locale-tw.js" type="text/javascript"></script>
	<script src="js/jquery.jqGrid.min.js" type="text/javascript"></script>
<style>
html{margin:0;padding:0}
body{margin:0;padding:10px}
.ui-jqgrid{margin-bottom:10px;}
.btn-active{color:red;}
</style>
	</head>
<body>
	<div style="margin-bottom:10px;">
		<div style="margin-bottom:5px;"><a href="https://www.excellence.com.hk/ebookshelf/" target="_blank">Excellence eBookShelf</a> 管理系統</div>
		<button id="btn-3" data-id="p3" class="btn">學校</button> 
		<button id="btn-4" data-id="p4" class="btn">教師</button> 
		<button id="btn-5" data-id="p5" class="btn">班級</button> 
		<button id="btn-6" data-id="p6" class="btn">學生</button> 
		<button id="btn-7" data-id="p7" class="btn">書冊</button> 
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<button class="bookshelf-btn">回到書櫃</button> 
		<button class="logout-btn">登出</button> 
	</div>

	<div id="p3" class="page" style="margin-bottom:10px;">
		<table id="jqGrid-3"></table>
		<div id="jqGridPager-3"></div>
	</div>

	<div id="p4" class="page" style="margin-bottom:10px;">
		<table id="jqGrid"></table>
		<div id="jqGridPager"></div>
	</div>
	
	<div id="p5" class="page" style="margin-bottom:10px;">
		<table id="jqGrid-4"></table>
		<div id="jqGridPager-4"></div>
	</div>
	
	<div id="p6" class="page" style="margin-bottom:10px;">
		<table id="jqGrid-2"></table>
		<div id="jqGridPager-2"></div>
	</div>

	<div id="p7" class="page" style="margin-bottom:10px;">
		<table id="jqGrid-7"></table>
		<div id="jqGridPager-7"></div>
	</div>
	
	
	<div id="dialogTeacherBook" class="eBookDialog" title="書本權限設定" style="overflow:hidden;" >
		<div class="textWarning"></div>
		教師帳戶：<span id="dialogTBaccount"></span><br>
		教師名稱：<span id="dialogTBname"></span><br>
		書本權限：<span style="color:red;font-size:14px;">按住控制（CTRL）鍵選擇多個選項</span><br>
		<select multiple="multiple" size="13" id="dialogTBbooks" style="width:450px;">
	<?php
	foreach ( $_SESSION['booklist'] as $booklist ) {
		echo '<option value="'.$booklist[0].'">'.$booklist[1].'</option>';
	}
	?>
		</select>
		<input type="hidden" id="dialogTBid" value="">
	</div>

	<div id="dialogClassBook" class="eBookDialog" title="書本權限設定" style="overflow:hidden;" >
		<div class="textWarning"></div>
		班級名稱：<span id="dialogCBname"></span><br>
		書本權限：<span style="color:red;font-size:14px;">按住控制（CTRL）鍵選擇多個選項</span><br>
		<select multiple="multiple" size="13" id="dialogCBbooks" style="width:450px;">
	<?php
	foreach ( $_SESSION['booklist'] as $booklist ) {
		echo '<option value="'.$booklist[0].'">'.$booklist[1].'</option>';
	}
	?>
		</select>
		<input type="hidden" id="dialogCBid" value="">
	</div>
	
	
<script type="text/javascript">
$(document).ready(function () {
	
	var teacher_id;
	var NumOfRow = Math.floor(($(window).height()-170)/25);
	var book = [];
	loadBook();
	var lastsel2;
	
	$( "#dialogClassBook" ).dialog({
 		autoOpen: false,
		width: 500,
		height: 500,
		resizable: false,
		modal: true,
		draggable: true,
		position: { my: "center", at: "center", of: window },
		buttons: [
			{
				text: '儲存',
				click: function(event){
					$('.textWarning').text('').hide();
					var classid = $('#dialogCBid').val();
					var books = [];
					$.each($("#dialogCBbooks option:selected"), function(){
						books.push($(this).val());
					});
					if ( books.length == 0 ) {
						$("<div title='注意'>請至少選取一本書籍。</div>").dialog({
							modal:true,
							close: function( event, ui ) {
								$('#dialogCBbooks').focus();
							}
						});
						return false;
					}
					
					// save data
					$.ajax({
						async: false,
						dataType: "json",
						type: 'POST',
						url: 'load/class_book_save.php',
						data: {
							'id': classid,
							'books': books,
							'oper': 'edit'
						},
						beforeSend: function() {
							
						},
						success: function(response) {
							if ( response.success ) {
								$("#jqGrid-4").trigger("reloadGrid");
								$( "#dialogClassBook" ).dialog('close');
							} else {
								$(".testWarning").text("儲存失敗，請再嘗試。").show();
							}
						},
						error: function (xhr,status,error) {
							$(".testWarning").text("同步失敗，請再嘗試。").show();
						}
					});

					
				}
			},
			{
				text: '取消',
				click: function(event){
					$( this ).dialog("close");
				}
			},
		],
		open: function( event, ui ) {
			
			var selRowId = $("#jqGrid-4").jqGrid("getGridParam", "selrow");
			var row = $("#jqGrid-4").getRowData(selRowId);
			$('#dialogCBid').val(row['class_id']);
			$('#dialogCBname').text(row['class_name']);
			$("#dialogCBbooks option:selected").prop("selected", false);
			var books = row['books'].split(',');
			$.each(books, function( index, value ){
				$('#dialogCBbooks option[value=' + value + ']').prop('selected', true);
			});
			
		},
		close: function( event, ui ) {

		},
		create: function(event,ui) {

		}
	});

	$( "#dialogTeacherBook" ).dialog({
 		autoOpen: false,
		width: 500,
		height: 500,
		resizable: false,
		modal: true,
		draggable: true,
		position: { my: "center", at: "center", of: window },
		buttons: [
			{
				text: '儲存',
				click: function(event){
					$('.textWarning').text('').hide();
					var teacherid = $('#dialogTBid').val();
					var books = [];
					$.each($("#dialogTBbooks option:selected"), function(){
						books.push($(this).val());
					});
					if ( books.length == 0 ) {
						$("<div title='注意'>請至少選取一本書籍。</div>").dialog({
							modal:true,
							close: function( event, ui ) {
								$('#dialogTBbooks').focus();
							}
						});
						return false;
					}
					
					// save data
					$.ajax({
						async: false,
						dataType: "json",
						type: 'POST',
						url: 'load/teacher_book_save.php',
						data: {
							'id': teacherid,
							'books': books,
							'oper': 'edit'
						},
						beforeSend: function() {
							
						},
						success: function(response) {
							if ( response.success ) {
								$("#jqGrid").trigger("reloadGrid");
								$( "#dialogTeacherBook" ).dialog('close');
							} else {
								$(".testWarning").text("儲存失敗，請再嘗試。").show();
							}
						},
						error: function (xhr,status,error) {
							$(".testWarning").text("同步失敗，請再嘗試。").show();
						}
					});

					
				}
			},
			{
				text: '取消',
				click: function(event){
					$( this ).dialog("close");
				}
			},
		],
		open: function( event, ui ) {
			
			var selRowId = $("#jqGrid").jqGrid("getGridParam", "selrow");
			var row = $("#jqGrid").getRowData(selRowId);
			$('#dialogTBid').val(row['teacher_id']);
			$('#dialogTBname').text(row['teacher_name']);
			$('#dialogTBaccount').text(row['teacher_login']);
			$("#dialogTBbooks option:selected").prop("selected", false);
			var books = row['books'].split(',');
			$.each(books, function( index, value ){
				$('#dialogTBbooks option[value=' + value + ']').prop('selected', true);
			});
			
		},
		close: function( event, ui ) {

		},
		create: function(event,ui) {

		}
	});
	
	$("#jqGrid-7").jqGrid({
		url: 'load/book_json.php',
		mtype: "POST",
		postData: {},
		// cellEdit:false,
		datatype: "json",
		jsonReader: {
			repeatitems : false,
			id: "0"
		},
		sortname: 'book_id',
		sortorder: 'asc',
		multiselect: false,
		colModel: [
			{ label: 'ID', name: 'book_id', width: 5, editable:false, editoptions: { readonly: "readonly" }, align: 'center' },
			{ label: 'Folder Name', name: 'book_path', width: 10, editable:true, align: 'center' },
			{ label: '書名', name: 'book_title', width: 20, editable:true, editrules : { required: true}, editoptions: { size: 20 }, align: 'center' },
			{ label: '封面', name: 'book_pic', width: 20, editable:true, editrules : { required: true}, editoptions: { size: 20 }, align: 'center' },
			{ label: '停用', name: 'disabled', width: 5, editable:true, align: 'center', hidden: false }
		],
		onSelectRow: function(id){
			// console.log(id);
			// if(id && id!==lastsel2){
				// console.log(id);
				// $('#jqGrid-7').jqGrid('restoreRow',lastsel2);
				// $('#jqGrid-7').jqGrid('editRow',id, {
					// keys: true
				// });
				// lastsel2=id;
			// }
		},
		viewrecords: true,
		width: $(window).width()-20,
		height: 'auto',
		rowNum: NumOfRow,
		loadComplete: function() {},
		editurl: 'load/book_save.php',
		pager: "#jqGridPager-7"
	});

	$('#jqGrid-7').navGrid('#jqGridPager-7',
		// the buttons to appear on the toolbar of the grid
		{ edit: true, add: true, del: false, search: true, refresh: false, view: false, position: "left", cloneToTop: false },
		// options for the Edit Dialog
		{},
		// options for the Add Dialog
		{},
		// options for the Del Dialog
		{}
	);

	
	$("#jqGrid-2").jqGrid({
		url: 'load/student_json.php',
		mtype: "POST",
		postData: {},
		cellEdit:false,
		datatype: "json",
		jsonReader: {
			repeatitems : false,
			id: "0"
		},
		sortname: 'student_id',
		sortorder: 'asc',
		multiselect: false,
		colModel: [
			{ label: '班號', name: 'class_id', width: 5, editable:false, editoptions: { readonly: "readonly" }, align: 'center' },
			{ label: '登入名稱(不能修改)', name: 'student_id', width: 15, editable:false, align: 'center' },
			{ label: '學生名稱', name: 'student_name', width: 20, editable:true, editrules : { required: true}, editoptions: { size: 20 }, align: 'center' },
			{ label: '密碼', name: 'pwd', width: 20, editable:true, editrules : { required: true}, editoptions: { size: 20 }, align: 'center' }
		],
		onSelectRow: function(id){
			console.log(id);
			if(id && id!==lastsel2){
				console.log(id);
				$('#jqGrid-2').jqGrid('restoreRow',lastsel2);
				$('#jqGrid-2').jqGrid('editRow',id, {
					keys: true
				});
				lastsel2=id;
			}
		},
		viewrecords: true,
		width: $(window).width()-20,
		height: 'auto',
		rowNum: NumOfRow,
		loadComplete: function() {},
		editurl: 'load/student_save.php',
		pager: "#jqGridPager-2"
	});

	$('#jqGrid-2').navGrid('#jqGridPager-2',
		// the buttons to appear on the toolbar of the grid
		{ edit: false, add: false, del: true, search: true, refresh: false, view: false, position: "left", cloneToTop: false },
		// options for the Edit Dialog
		{},
		// options for the Add Dialog
		{
			addCaption: "新增學生",
			closeAfterAdd: true,
			recreateForm: true,
			checkOnUpdate : false,
			viewPagerButtons: false,
			modal: true,
			left: ( $(window).width()-500 ) /2,
			top: ( $(window).height()-210 ) /2,
			afterShowForm : function (formid) {
				// $('#class_id').val(class_id);
			},
			errorTextFormat: function (data) {
				return 'Error: ' + data.responseText
			},
			afterSubmit : function(response, postdata) {
				var resp = JSON.parse(response.responseText);
				return [resp.success,resp.err,resp.new_id];
			}
		},
		// options for the Del Dialog
		{
			left: ( $(window).width()-250 ) /2,
			top: ( $(window).height()-132 ) /2,
		}
	);

	
	$("#jqGrid-4").jqGrid({
		url: 'load/class_json.php',
		mtype: "POST",
		postData: {book:'1'},
		datatype: "json",
		jsonReader: {
			repeatitems : false,
			id: "0"
		},
		sortname: 'class_id',
		sortorder: 'asc',
		multiselect: false,
		colModel: [
			{ label: 'ID', name: 'class_id', width: 10, editable:false, align: 'center' },
			{ label: '班級名稱', name: 'class_name', width: 10, editable:true, align: 'center', editrules : { required: true},editoptions: { size: 50}  },
			{ label: '人數', name: 'student_count', width: 10, editable:true, editrules : { required: true, integer: true, minValue: 1}, editoptions: { size: 10, readonly: 'readonly'}, align: 'center' },
			{ label: '書本權限', name: 'books2', width: 30, editable:false, align: 'center', formatter:bookFormat },
			{ label: '書本', name: 'books', width: 30, editable:false, align: 'center', hidden: true },
			{ label: '教師', name: 'teacher_id', width: 10, search:true, stype:'select', searchoptions:{sopt:['eq']}, editable: true, edittype: "select", formatter: "select", editoptions: {} },
			{ label: '學校代碼', name: 'school_code', width: 10, editable: false },
			{ label: '修改日期', name: 'edit_date', width: 15, editable:false, align: 'center' },
			{ label: '刪除', name: 'deleted', width: 5, editable:false, align: 'center', hidden: true }
		],
		viewrecords: true,
		width: $(window).width()-20,
		height: 'auto',
		rowNum: NumOfRow,
		loadComplete: function() {},
		onSelectRow: function(ids) {},
		editurl: 'load/class_save.php',
		pager: "#jqGridPager-4"
	});

	$('#jqGrid-4').navGrid('#jqGridPager-4',
		// the buttons to appear on the toolbar of the grid
		{ edit: true, add: true, del: true, search: true, refresh: true, view: false, position: "left", cloneToTop: false },
		// options for the Edit Dialog
		{
			editCaption: "修改班級",
			closeAfterAdd: true,
			recreateForm: true,
			checkOnUpdate : false,
			viewPagerButtons: false,
			modal: true,
			left: ( $(window).width()-500 ) /2,
			top: ( $(window).height()-210 ) /2,
			afterShowForm : function (formid) {
				// $('#class_id').val(class_id);
				$('#student_count').attr("readonly", true);
			},
			errorTextFormat: function (data) {
				return 'Error: ' + data.responseText
			},
			afterSubmit : function(response, postdata) {
				var resp = JSON.parse(response.responseText);
				return [resp.success,resp.err,resp.new_id];
			}
			
		},
		// options for the Add Dialog
		{
			addCaption: "新增班級",
			closeAfterAdd: true,
			recreateForm: true,
			checkOnUpdate : false,
			viewPagerButtons: false,
			modal: true,
			left: ( $(window).width()-500 ) /2,
			top: ( $(window).height()-210 ) /2,
			afterShowForm : function (formid) {
				// $('#class_id').val(class_id);
				$('#student_count').attr("readonly", false);
			},
			errorTextFormat: function (data) {
				return 'Error: ' + data.responseText
			},
			afterSubmit : function(response, postdata) {
				var resp = JSON.parse(response.responseText);
				return [resp.success,resp.err,resp.new_id];
			}
		},
		// options for the Del Dialog
		{
			left: ( $(window).width()-250 ) /2,
			top: ( $(window).height()-132 ) /2,
		}

	);
	
   $('#jqGrid-4').navButtonAdd('#jqGridPager-4',{
		buttonicon: "ui-icon-note",
		title: "書本權限設定",
		caption: "書本權限設定",
		position: "last",
		onClickButton: classBookClicked
	});
	
	function classBookClicked() {
		// alert("You have clicked a custom button.");
		var selRowId = $("#jqGrid-4").jqGrid("getGridParam", "selrow");
		if ( selRowId != null ) {
			// the row having rowId is selected
			console.log(selRowId);
			teacher_id = selRowId;
			$( "#dialogClassBook" ).dialog( "open" );
		}
	}


	
	var editOption = {
		editCaption: "修改教師",
		recreateForm: true,
		checkOnUpdate : false,
		checkOnSubmit : false,
		closeAfterEdit: true,
		viewPagerButtons: false,
		modal: true,
		left: ( $(window).width()-500 ) /2,
		top: ( $(window).height()-350 ) /2,
		afterShowForm : function (formid) {
			$('#teacher_login').attr("readonly", true);
		},
		errorTextFormat: function (data) {
			return 'Error: ' + data.responseText
		},
		afterSubmit : function(response, postdata) {
			var resp = JSON.parse(response.responseText);
			return [resp.success,resp.err,resp.new_id];
		}
	}

	
	$("#jqGrid").jqGrid({
		url: 'load/teacher_json.php',
		mtype: "POST",
		postData: {book:'1'},
		datatype: "json",
		jsonReader: {
			repeatitems : false,
			id: "0"
		},
		sortname: 'teacher_id',
		sortorder: 'asc',
		multiselect: false,
		colModel: [
			{ label: 'ID', name: 'teacher_id', key: true, width: 40, search:false, editable:false, align: 'center' },
			{ label: 'Login', name: 'teacher_login', width: 100, search:true, searchoptions:{sopt:['cn']}, searchrules:{required:true}, editable:true, editrules : { required: true},editoptions: { size: 50} },
			{ label: 'PWD', name: 'teacher_pwd', width: 100, search:false, editable:true, editrules : { required: true},editoptions: { size: 50} },
			{ label: 'Name', name: 'teacher_name', width: 100, search:true, searchoptions:{sopt:['cn']}, searchrules:{required:true}, editable:true, editrules : { required: true},editoptions: { size: 50} },
			{ label: 'School', name: 'school_id', width: 150, search:true, stype:'select', searchoptions:{sopt:['eq']}, editable: true, edittype: "select", formatter: "select", editoptions: {} },
			{ label: 'Expire', name: 'expire_date', width: 100, align: 'center', cellattr:valueToday, search:true, searchoptions:{sopt:['le','ge'],dataInit:getDate}, searchrules:{required:true,date:true}, editable: true, editrules : { required: true, date:true}, editoptions: {dataInit: getDate} },
			{ label: 'Last Login', name: 'last_login', width: 150, search:false, editable:false, align: 'center' },
			// { label: 'SID', name: 'sid', width: 150, editable:false },
			{ label: 'Wrong Count', name: 'wrong_count', width: 25, align: 'center', cellattr:valueWrong, search:false, editable:true, editrules : { required: true, integer: true, minValue: 0, maxValue:10},editoptions: { size: 10} },
			{ label: 'Disabled', name: 'disabled', width: 50, align: 'center', search:true, stype:'select', searchoptions:{sopt:['eq'],value:'0:False;1:True'}, editable: true, edittype: "select", formatter: "select", editoptions: {value:'0:False;1:True'},cellattr:valueAttr },
			// { label: 'edit_user', name: 'edit_user', width: 50, search:false, editable:false, align: 'center' },
			// { label: 'edit_date', name: 'edit_date', width: 150, search:false, editable:false, align: 'center' },
			{ label: 'Books', name: 'books', width: 200, search:false, editable:false, align: 'left', hidden: true },
			{ label: '書本權限', name: 'books2', width: 200, editable:false, align: 'center', formatter:bookFormat }
			
		],
		viewrecords: true,
		width: $(window).width()-20,
		height: 'auto',
		rowNum: NumOfRow,
		loadComplete: function() {},
		onSelectRow: function(ids) {},
		ondblClickRow: function(rowid) {
			jQuery(this).jqGrid('editGridRow', rowid, editOption);
		},
		editurl: 'load/teacher_save.php',
		pager: "#jqGridPager"
	});

	$('#jqGrid').navGrid('#jqGridPager',
		// the buttons to appear on the toolbar of the grid
		{ edit: true, add: true, del: false, search: true, refresh: true, view: false, position: "left", cloneToTop: false, addtitle: "新增教師", edittitle: "編輯已選教師", searchtitle: "搜尋"  },
		// options for the Edit Dialog
		editOption,
		// options for the Add Dialog
		{
			addCaption: "新增教師",
			closeAfterAdd: true,
			recreateForm: true,
			checkOnUpdate : false,
			viewPagerButtons: false,
			modal: true,
			left: ( $(window).width()-500 ) /2,
			top: ( $(window).height()-350 ) /2,
			afterShowForm : function (formid) {
				$('#teacher_login').attr("readonly", false);
				var randomstring = Math.random().toString(36).slice(-8);
				$('#teacher_pwd').val(randomstring);
				$('#wrong_count').val(0);
				
			},
			errorTextFormat: function (data) {
				return 'Error: ' + data.responseText
			},
			afterSubmit : function(response, postdata) {
				var resp = JSON.parse(response.responseText);
				return [resp.success,resp.err,resp.new_id];
			}
		}
	);
	
   $('#jqGrid').navButtonAdd('#jqGridPager',{
		buttonicon: "ui-icon-note",
		title: "書本權限設定",
		caption: "書本權限設定",
		position: "last",
		onClickButton: teacherBookClicked
	});
	
	function teacherBookClicked() {
		// alert("You have clicked a custom button.");
		var selRowId = $("#jqGrid").jqGrid("getGridParam", "selrow");
		if ( selRowId != null ) {
			// the row having rowId is selected
			console.log(selRowId);
			teacher_id = selRowId;
			$( "#dialogTeacherBook" ).dialog( "open" );
		}
	}
	
	var editOption3 = {
		editCaption: "修改學校",
		recreateForm: true,
		checkOnUpdate : false,
		checkOnSubmit : false,
		closeAfterEdit: true,
		viewPagerButtons: false,
		modal: true,
		left: ( $(window).width()-500 ) /2,
		top: ( $(window).height()-250 ) /2,
		afterShowForm : function (formid) {
			$('#schCode').attr("readonly", true);
		},
		errorTextFormat: function (data) {
			return 'Error: ' + data.responseText
		},
		afterSubmit : function(response, postdata) {
			var resp = JSON.parse(response.responseText);
			return [resp.success,resp.err,resp.new_id];
		},
		afterComplete : function (response, postdata, formid) {
			loadSchool();
		}
	}
	
	$("#jqGrid-3").jqGrid({
		url: 'load/school_json.php',
		mtype: "POST",
		postData: {book:'1'},
		datatype: "json",
		jsonReader: {
			repeatitems : false,
			id: "0"
		},
		sortname: 'schId',
		sortorder: 'asc',
		multiselect: false,
		colModel: [
			{ label: 'ID', name: 'schId', key: true, width: 40, editable:false, align: 'center' },
			{ label: 'Code', name: 'schCode', width: 40, editable:true, editrules : { required: true}, editoptions: { size: 50, readonly: 'readonly'}, align: 'center' },
			{ label: 'Chi Name', name: 'schCName', width: 150, editable:true, editrules : { required: true},editoptions: { size: 50} },
			{ label: 'Eng Name', name: 'schEName', width: 150, editable:true, editrules : { required: true},editoptions: { size: 50} },
			{ label: 'Address', name: 'schCAdd', width: 150, editable:true, editrules : { required: true},editoptions: { size: 50} }
			// { label: 'Lang', name: 'schLang', width: 50, align: 'center' },
		],
		viewrecords: true,
		width: $(window).width()-20,
		height: 'auto',
		rowNum: NumOfRow,
		loadComplete: function() {},
		onSelectRow: function(ids) {},
		ondblClickRow: function(rowid) {
			jQuery(this).jqGrid('editGridRow', rowid, editOption3);
		},
		editurl: 'load/school_save.php',
		pager: "#jqGridPager-3"
	});

	$('#jqGrid-3').navGrid('#jqGridPager-3',
		// the buttons to appear on the toolbar of the grid
		{ edit: true, add: true, del: false, search: false, refresh: false, view: false, position: "left", cloneToTop: false, addtitle: "新增學校", edittitle: "編輯已選學校" },
		// options for the Edit Dialog
		editOption3,
		// options for the Add Dialog
		{
			addCaption: "新增學校",
			closeAfterAdd: true,
			recreateForm: true,
			checkOnUpdate : false,
			viewPagerButtons: false,
			modal: true,
			left: ( $(window).width()-500 ) /2,
			top: ( $(window).height()-250 ) /2,
			afterShowForm : function (formid) {
				$('#schCode').attr("readonly", false);
			},
			errorTextFormat: function (data) {
				return 'Error: ' + data.responseText
			},
			afterSubmit : function(response, postdata) {
				var resp = JSON.parse(response.responseText);
				return [resp.success,resp.err,resp.new_id];
			},
			afterComplete : function (response, postdata, formid) {
				loadSchool();
			}
		}
	);
	
	$(window).resize(function() {
		var NumOfRow = Math.floor(($(window).height()-170)/25);
		$("#jqGrid").jqGrid('setGridWidth', $(window).width()-20);
		$("#jqGrid").jqGrid('setGridParam', {rowNum: NumOfRow}).trigger("reloadGrid");
		$("#jqGrid-3").jqGrid('setGridWidth', $(window).width()-20);
		$("#jqGrid-3").jqGrid('setGridParam', {rowNum: NumOfRow}).trigger("reloadGrid");
		$("#jqGrid-4").jqGrid('setGridWidth', $(window).width()-20);
		$("#jqGrid-4").jqGrid('setGridParam', {rowNum: NumOfRow}).trigger("reloadGrid");
		$("#jqGrid-7").jqGrid('setGridWidth', $(window).width()-20);
		$("#jqGrid-7").jqGrid('setGridParam', {rowNum: NumOfRow}).trigger("reloadGrid");
		
	});
		
	loadSchool();
	loadTeacher();
	
	$('.btn').click(function(e){
		var pageNo = $(this).attr('data-id');
		$('.page').hide();
		$('.btn').removeClass('btn-active');
		$(this).addClass('btn-active');
		$('#'+pageNo).show();
	});
	
	$('.logout-btn').click(function(e){
		window.location.href = "../logout.php";
	});
	
	$('.bookshelf-btn').click(function(e){
		window.location.href = "../index.php";
	});

	$('#btn-3').click();
});

function loadSchool() {
	$.ajax({
	  url: "load/school_select.php"
	}).done(function( data ) {
	  $('#jqGrid').setColProp('school_id',{editoptions:{value:data}}).trigger("reloadGrid");
	  $('#jqGrid').setColProp('school_id',{searchoptions:{sopt:['eq'],value:data}}).trigger("reloadGrid");
	  // searchoptions:{sopt:['eq']}
	});
}

function loadTeacher() {
	$.ajax({
	  url: "load/teacher_select.php"
	}).done(function( data ) {
	  $('#jqGrid-4').setColProp('teacher_id',{editoptions:{value:data}}).trigger("reloadGrid");
	  $('#jqGrid-4').setColProp('teacher_id',{searchoptions:{sopt:['eq'],value:data}}).trigger("reloadGrid");
	});
}

// function loadBook() {
	// $.ajax({
	  // url: "load/book_select.php"
	// }).done(function( data ) {
	  // $('#jqGrid-2').setColProp('book_id',{editoptions:{value:data}}).trigger("reloadGrid");
	// });
// }

function loadBook() {
	$.ajax({
	  url: "load/books.php",
	  dataType: "json",
	  async: false,
	}).done(function( data ) {
	  book = data.rows;
	});
}

function bookFormat(cellvalue, options, rowObject) {
	if ( cellvalue == "" ) {
		return "";
	} else {
		var i;
		var books = cellvalue.split(',');
		var booktext = '';
		for (i = 0; i < books.length; i++) {
			booktext += book[ books[i]-1 ]['book_title'] + "<br>";
		}
		return booktext;
	}
}

function valueAttr(rowId, cellValue, rawObject, cm, rdata) {
	if (cellValue == 'True') {
		return ' style="color:red"';
	} else if (cellValue == 'False') {
		return ' style="color:green"';
	}
}

function valueToday(rowId, cellValue, rawObject, cm, rdata) {
	var inputDate = new Date(cellValue);
	var todaysDate = new Date();
	
	if(inputDate.setHours(0,0,0,0) >= todaysDate.setHours(0,0,0,0)) {
		return ' style="color:green"';
	} else {
		return ' style="color:red"';
	}
}

function valueWrong(rowId, cellValue, rawObject, cm, rdata) {
	if ( parseInt(cellValue) >= 10 ) {
		return ' style="color:red"';
	} else {
		return ' style="color:green"';
	}
}

function getDate(element) {
	$(element).datepicker({
		dateFormat: 'yy-mm-dd',
		showOn: 'focus'
	});
}

</script>
</body>
</html>


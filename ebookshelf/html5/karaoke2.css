html {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
body {
	margin: 0;
	padding: 0;
	width: 800px;
	height: 600px;
	overflow: hidden;
	transform: scale(1,1);
	transform-origin: 0% 0% 0;
}
#main {
	width: 800px;
	height: 600px;
	margin: 0 auto;
	overflow: hidden;
	position: relative;
	background-color:black;
	z-index: -1;
}
*:focus {
    outline: none;
}
.button {
	position: absolute;
	border: none;
	background: none;
	background-size: contain;
	cursor: pointer;
	
}
.button:active{
	margin-top: 1px;
}
#play-start-btn {
	width: 120px;
	height: 80px;
	top: 500px;
	left: 340px;
	background-image: url(karaoke2/R_play.png);
	background-repeat: no-repeat;
	background-size: contain;
}

#play-start-btn:hover {
	background-image: url(karaoke2/R_play_over.png);
}

#play-btn {
	width: 120px;
	height: 80px;
	top: 500px;
	left: 230px;
	background-repeat: no-repeat;
	background-size: contain;
}
#play-btn.playing {
	background-image: url(karaoke2/Pause.png);
}
#play-btn.playing:hover {
	background-image: url(karaoke2/Pause_over.png);
}
#play-btn.paused {
	background-image: url(karaoke2/Play.png);
}
#play-btn.paused:hover {
	background-image: url(karaoke2/Play_over.png);
}
#demo-btn {
	width: 120px;
	height: 80px;
	top: 500px;
	left: 480px;
	background-repeat: no-repeat;
	background-size: contain;
}
#demo-btn.demo {
	background-image: url(karaoke2/Sing.png);
}
#demo-btn.demo:hover {
	background-image: url(karaoke2/Sing_over.png);
}
#demo-btn.accomp {
	background-image: url(karaoke2/A_sing.png);
}
#demo-btn.accomp:hover {
	background-image: url(karaoke2/A_sing_over.png);
}
#vid2 {
	z-index: -1;
}
$( document ).ready(function() {

	$("#ans-btn").hide();
	
	var student = $( "#dialogHtml5", window.parent.document ).attr('data-student');

	shuffle(matchAns);

	for (i = 0; i < matchAns.length; i++) {
		if ( matchAns[i].img == "" ) {
			$("span.match-draggable:eq(" + i + ")" ).text( matchAns[i].ans );
		} else {
			$("span.match-draggable:eq(" + i + ")" ).html( '<img src="img/' + matchAns[i].img + '" alt="' + matchAns[i].ans + '" class="dragImg" >' );
		}
		$("span.match-draggable:eq(" + i + ")" ).attr("data-ans", matchAns[i].ans );
	}

	// get result
	$.ajax({
		async: false,
		dataType: "json",
		type: 'POST',
		url: '/ebookshelf/load/getResult.php',
		data: {
			'book_id': book_id,
			'page': page,
			'number': number,
			'student': student,
		},
		beforeSend: function() {
			$("div.warning").text("資訊同步中...");
		},
		success: function(response) {
			if (response.done) {
				// display answer
				if ( bookLang == 'eng' ) {
					$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
				} else {
					$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
				}

				var i = 0;
				$('span.ans').each(function() {
					$(this).attr("data-drop",response.result[i] );
					$(this).append( $('span.match-draggable[data-ans="' + response.result[i] + '"]') );
					i++;
				});

				$("span.match-draggable").each(function() {

				});

			} else {
				
				if (response.type==2) {
					$("#ans-btn").show();
					if ( bookLang == 'eng' ) {
						$("div.warning").text("This test can only be submitted once. Be cautious.");
					} else {
						$("div.warning").text("本測驗只能提交一次，請小心使用。");
					}
				} else {
					if ( bookLang == 'eng' ) {
						$("#ans-btn").text('View Results').show();
					} else {
						$("#ans-btn").text('測驗結果').show();
					}
					$("div.warning").text("");					
				}

				$(".match-draggable").draggable({
					// revert: "invalid",
					revert : function(event, ui) {
						// on older version of jQuery use "draggable"
						// $(this).data("draggable")
						$(this).data("uiDraggable").originalPosition = {
							top : 0,
							left : 0
						};

						return !event;
						// return (event !== false) ? false : true;
					},

					containment: "window",
					start: function(event, ui) {
						// ui.position.left = 0;
						// ui.position.top = 0;
					},
					drag: function(event, ui) {

						var changeLeft = ui.position.left - ui.originalPosition.left; // find change in left
						var newLeft = ui.originalPosition.left + changeLeft / (( zoomLevel)); // adjust new left by our zoomScale

						var changeTop = ui.position.top - ui.originalPosition.top; // find change in top
						var newTop = ui.originalPosition.top + changeTop / zoomLevel; // adjust new top by our zoomScale

						ui.position.left = newLeft;
						ui.position.top = newTop;

					}
				});

				$(".match-droppable").droppable({
					hoverClass: "match-active",
					drop: function( event, ui ) {
						$(this).droppable('option', 'accept', ui.draggable);
						$(this).attr("data-drop", ui.draggable.attr('data-ans') );

						$(ui.draggable).position({
							my: "center",
							at: "center",
							of: $(this)
						});

					},
					out: function(event, ui){
						$(this).droppable('option', 'accept', '*');
						$(this).attr("data-drop", "" );
					}
				});


			}
		},
		error: function (xhr,status,error) {
			$("div.warning").text("同步失敗，請再嘗試。");
		}
	});

	$("#ans-btn").click(function() {
		
		if ( $("#ans-btn").text()=="提交" || $("#ans-btn").text()=="Submit" ) {

			$("#ans-btn").hide();

			var mark = 0;
			var result = [];
			var total = 0;

			$('span.ans').each(function() {
				var ans1 = $(this).attr('data-ans').split(",");
				var ans2 = $(this).attr("data-drop");
				result.push( ans2 );
				total++;
				if ( $.inArray(ans2,ans1 ) > "-1" ) mark++;
			});

			// save result
			$.ajax({
				async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/saveResult.php',
				data: {
					'book_id': book_id,
					'page': page,
					'number': number,
					'result': result,
					'mark': mark,
					'total': total,
				},
				beforeSend: function() {
					$("div.warning").text("資料同步中.....");
				},
				success: function(response) {
					if (response.done) {
						// display answer
						$("#ans-btn").hide();
						if ( bookLang == 'eng' ) {
							$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
						} else {
							$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
						}

						$(".match-draggable").draggable({ disabled: true });

					} else {
						$("#ans-btn").show();

					}
				}
			});
		} else if ( $("#ans-btn").text()=="測驗結果" || $("#ans-btn").text()=="View Results" ) {
			var src = window.parent.$('#myHtml5').attr('src');
			window.parent.openTestDialog2(src,page,number);
		}

	});



	// old codes
	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});

	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );

	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});

	var audio = new Audio();

	audio.onended = function(){
		$('.track-btn').addClass('disable');
	};

	$('.track-btn').on('click', function() {
		if ( $(this).hasClass('disable') ) {
			var src = 'mp3/' + $(this).attr('data-mp3');
			$('.track-btn').addClass('disable');
			$(this).removeClass('disable');
			audio.src = src;
			audio.play();
		} else {
			$('.track-btn').addClass('disable');
			audio.pause();
		}
	});

	function shuffle(array) {
		var tmp, current, top = array.length;

		if(top) while(--top) {
			current = Math.floor(Math.random() * (top + 1));
			tmp = array[current];
			array[current] = array[top];
			array[top] = tmp;
		}

		return array;
	}

});

$( document ).ready(function() {
	// 開放題型
	$("#ans-btn").hide();
	
	var student = $( "#dialogHtml5", window.parent.document ).attr('data-student');
	
	// get result
	$.ajax({
		async: false,
		dataType: "json",
		type: 'POST',
		url: '/ebookshelf/load/getResult.php',
		data: {
			'book_id': book_id,
			'page': page,
			'number': number,
			'student': student,
		},
		beforeSend: function() {
			$("td.warning").text("資訊同步中...");
		},
		success: function(response) {
			if (response.done) {
				// display answer
				if ( bookLang == 'eng' ) {
					$("td.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
				} else {
					$("td.warning").text("測驗已於" + response.submit + "提交。謝謝。");
				}
				
				var i = 0;
				$('input.ans').each(function() {
					$(this).val( response.result[i] ).prop('readonly', true);;
					i++;
				});
				
			} else {
				
				if (response.type==2) {
					$("#ans-btn").show();
					if ( bookLang == 'eng' ) {
						$("td.warning").text("This test can only be submitted once. Be cautious.");
					} else {
						$("td.warning").text("本測驗只能提交一次，請小心使用。");
					}
				} else {
					if ( bookLang == 'eng' ) {
						$("#ans-btn").text('View Results').show();
					} else {
						$("#ans-btn").text('測驗結果').show();
					}
					$("td.warning").text("");
				}

			}
		},
		error: function (xhr,status,error) {
			$("td.warning").text("同步失敗，請再嘗試。");
		}
	});

	// old codes
	
	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	
	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );

	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});
	
	$("#ans-btn").click(function() {
		
		if ( $("#ans-btn").text()=="提交" || $("#ans-btn").text()=="Submit" ) {
		
			$("#ans-btn").hide();
			
			var mark = 0;
			var result = [];
			var total = 0;
			
			$('input.ans').each(function() {
				var ans2 = $.trim( $(this).val() );
				result.push( ans2 );
			});
			
			// save result
			$.ajax({
				async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/saveResult.php',
				data: {
					'book_id': book_id,
					'page': page,
					'number': number,
					'result': result,
					'mark': mark,
					'total': total,
				},
				beforeSend: function() {
					$("td.warning").text("資料同步中.....");
				},
				success: function(response) {
					if (response.done) {
						// display answer
						$("#ans-btn").hide();
						if ( bookLang == 'eng' ) {
							$("td.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
						} else {
							$("td.warning").text("測驗已於" + response.submit + "提交。謝謝。");
						}
						
						$('input.ans').each(function() {
							$(this).prop('readonly', true);
						});
						
					} else {
						$("#ans-btn").show();
						
					}
				}
			});
		} else if ( $("#ans-btn").text()=="測驗結果" || $("#ans-btn").text()=="View Results" ) {
			var src = window.parent.$('#myHtml5').attr('src');
			window.parent.openTestDialog2(src,page,number);
		}
		
	});
	
	var audio = new Audio();
	
	audio.onended = function(){
		$('.track-btn').addClass('disable');
	};
	
	$('.track-btn').on('click', function() {
		if ( $(this).hasClass('disable') ) {
			var src = 'mp3/' + $(this).attr('data-mp3');
			$('.track-btn').addClass('disable');
			$(this).removeClass('disable');
			audio.src = src;
			audio.play();
		} else {
			$('.track-btn').addClass('disable');
			audio.pause();
		}
	});


});


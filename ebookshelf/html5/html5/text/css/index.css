html{
	margin: 0;
	padding:0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

body {
	margin: 0;
	padding:0;
	width: 800px;
	height: 600px;
	overflow: hidden;
	transform: scale(1,1);
	transform-origin: 0% 0% 0;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
.track-btn {
    opacity: 1;
    filter: alpha(opacity=100); /* msie */
	cursor: pointer;
	vertical-align:middle;
}

.track-btn.disable {
    opacity: 0.4;
    filter: alpha(opacity=40); /* msie */
	cursor: pointer;
}

#main {
	width: 800px;
	height: 600px;
	margin: auto;
}

#table1 {
	padding: 0;
}

td {
	position: relative;
	vertical-align: top;
}

textarea, input {
	font-size: 24px;
	color: #000000;
	border: none;
	border-bottom: 1px solid #000000;
	text-align: center;
	border-radius: 0px;
}

.tick {
	position: relative;
	top: 0px;
	left: 0px;
	vertical-align: middle;
	margin-left: -15px;
	margin-right: -10px;
	display: none;
}


#teacher-btn {
	position: fixed;
	top: 25px;
	right: 25px;
}


.teacher {
	color: #ec008c;
}

span.title {
	color: brown;
	font-weight: 900;
}

.indent {
	text-indent : 40px;
	margin: 0;
}

div.round {
	margin: 10px;
	background-color: #e8f3eb;
	padding: 20px;
	border-radius: 15px;
	border: 2px solid #000000;
}

table.software {
	border-collapse: collapse;
	border: 0px solid #000000;
}

table.software th {
	background-color: #fef79c;
	border: 1px solid #000000;
}

table.software td {
	text-align: center;
	border: 1px solid #000000;
}

span.ref {
	display: none;
	color: #ec008c;
	font-size: 16px;
}

table.software th.clear {
	border: none;
	background-color: transparent;
}

.myButton {
	-moz-box-shadow:inset 0px 1px 0px 0px #f9eca0;
	-webkit-box-shadow:inset 0px 1px 0px 0px #f9eca0;
	box-shadow:inset 0px 1px 0px 0px #f9eca0;
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #f0c911), color-stop(1, #f2ab1e));
	background:-moz-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-webkit-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-o-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-ms-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:linear-gradient(to bottom, #f0c911 5%, #f2ab1e 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f0c911', endColorstr='#f2ab1e',GradientType=0);
	background-color:#f0c911;
	-moz-border-radius:6px;
	-webkit-border-radius:6px;
	border-radius:6px;
	border:1px solid #e65f44;
	display:inline-block;
	cursor:pointer;
	color:#c92200;
	font-family:Arial;
	font-size:28px;
	font-weight:bold;
	padding:10px 24px;
	text-decoration:none;
	text-shadow:0px 1px 0px #ded17c;
}
.myButton:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #f2ab1e), color-stop(1, #f0c911));
	background:-moz-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-webkit-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-o-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-ms-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:linear-gradient(to bottom, #f2ab1e 5%, #f0c911 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2ab1e', endColorstr='#f0c911',GradientType=0);
	background-color:#f2ab1e;
}

.myButton:active {
	position:relative;
	top:1px;	
}
.myButton:focus {
	outline: none;
}

$( document ).ready(function() {
	var learningData = new HKECLearningData();
	var key = "p23";
	var temp_q1_1 = "";
	var temp_q1_2 = "";
	var temp_q1_3 = "";
	var temp_q1_4 = "";
	var temp_q1_5 = "";
	var temp_q1_6 = "";
	var temp_q1_7 = "";
	var temp_q1_8 = "";
	var temp_q1_9 = "";
	var temp_q1_10 = "";
	
	var ans1 = ["像素"];
	var ans2 = ["數學公式"];
	var ans3 = ["大"];
	var ans4 = ["小"];
	var ans5 = ["會","有"];
	var ans6 = ["不會","沒有"];
	var ans7 = ["難","困難","複雜"];
	var ans8 = ["易","容易","簡單"];
	var ans9 = ["相片","真實相片","景物"];
	var ans10 = ["圖形","簡單圖形","圖表"];

	setTimeout(function(){ 
	
		learningData.init(function(){
			
			learningData.get(key, function(err, result){ 
				// handle retrieval				
				if (result === null) {
					var data = {
						'run_count':1,
						'run_last':today(),
						'q1_1':'',
						'q1_2':'',
						'q1_3':'',
						'q1_4':'',
						'q1_5':'',
						'q1_6':'',
						'q1_7':'',
						'q1_8':'',
						'q1_9':'',
						'q1_10':''
					};
					learningData.set(key, data, function(err){
						// handle saving
					}); 
				} else {
					var run_count, q1_1, q1_2, q1_3, q1_4, q1_5, q1_6, q1_7, q1_8, q1_9, q1_10;
					"run_count" in result ? run_count = result.run_count+1 : run_count=1;
					
					"q1_1" in result ? q1_1 = result.q1_1 : q1_1='';
					"q1_2" in result ? q1_2 = result.q1_2 : q1_2='';
					"q1_3" in result ? q1_3 = result.q1_3 : q1_3='';
					"q1_4" in result ? q1_4 = result.q1_4 : q1_4='';
					"q1_5" in result ? q1_5 = result.q1_5 : q1_5='';
					"q1_6" in result ? q1_6 = result.q1_6 : q1_6='';
					"q1_7" in result ? q1_7 = result.q1_7 : q1_7='';
					"q1_8" in result ? q1_8 = result.q1_8 : q1_8='';
					"q1_9" in result ? q1_9 = result.q1_9 : q1_9='';
					"q1_10" in result ? q1_10 = result.q1_10 : q1_10='';

					$("#q-1-1").val(q1_1);
					$("#q-1-2").val(q1_2);
					$("#q-1-3").val(q1_3);
					$("#q-1-4").val(q1_4);
					$("#q-1-5").val(q1_5);
					$("#q-1-6").val(q1_6);
					$("#q-1-7").val(q1_7);
					$("#q-1-8").val(q1_8);
					$("#q-1-9").val(q1_9);
					$("#q-1-10").val(q1_10);
					
					var data = {
						'run_count':run_count,
						'run_last':today(),
						'q1_1':q1_1,
						'q1_2':q1_2,
						'q1_3':q1_3,
						'q1_4':q1_4,
						'q1_5':q1_5,
						'q1_6':q1_6,
						'q1_7':q1_7,
						'q1_8':q1_8,
						'q1_9':q1_9,
						'q1_10':q1_10
					};

					learningData.set(key, data, function(err){
						// handle saving
					});
				}
				
			}); 
		});
	
	}, 1000);
	
	function today() {
		var d = new Date();
		var month = d.getMonth()+1;
		var day = d.getDate();
		var hour = d.getHours();
		var min = d.getMinutes();
		var sec = d.getSeconds();
		return d.getFullYear() + '-' + (month<10 ? '0' : '') + month + '-' + (day<10 ? '0' : '') + day + ' ' + (hour<10 ? '0' : '') + hour + ':' + (min<10 ? '0' : '') + min + ':' + (sec<10 ? '0' : '') + sec;
	}
	
	$("#teacher-btn").hide();
	
	$("#teacher-btn").click(function() {
	
		// alert($("#q-1-1").hasClass("teacher"));
		$("img.tick").hide();
	
		if ( $("#q-1-1").hasClass("teacher") ) {
			$("#q-1-1").val(temp_q1_1);
			$("#q-1-2").val(temp_q1_2);
			$("#q-1-3").val(temp_q1_3);
			$("#q-1-4").val(temp_q1_4);
			$("#q-1-5").val(temp_q1_5);
			$("#q-1-6").val(temp_q1_6);
			$("#q-1-7").val(temp_q1_7);
			$("#q-1-8").val(temp_q1_8);
			$("#q-1-9").val(temp_q1_9);
			$("#q-1-10").val(temp_q1_10);
			
			$("input[id^='q-1']").removeClass("teacher");
			
			$("span.ref").hide();
		} else {
			temp_q1_1 = $("#q-1-1").val();
			temp_q1_2 = $("#q-1-2").val();
			temp_q1_3 = $("#q-1-3").val();
			temp_q1_4 = $("#q-1-4").val();
			temp_q1_5 = $("#q-1-5").val();
			temp_q1_6 = $("#q-1-6").val();
			temp_q1_7 = $("#q-1-7").val();
			temp_q1_8 = $("#q-1-8").val();
			temp_q1_9 = $("#q-1-9").val();
			temp_q1_10 = $("#q-1-10").val();
			
			$("input[id^='q-1']").addClass("teacher");
			
			$("#q-1-1").val("像素");
			$("#q-1-2").val("數學公式");
			$("#q-1-3").val("大");
			$("#q-1-4").val("小");
			$("#q-1-5").val("會");
			$("#q-1-6").val("不會");
			$("#q-1-7").val("難");
			$("#q-1-8").val("易");
			$("#q-1-9").val("真實相片或景物");
			$("#q-1-10").val("簡單圖形或圖表");

			$("span.ref").show();
		}
	});

	$("#ans-btn").click(function() {
		
		var ans_1 = $("#q-1-1").val();
		var ans_2 = $("#q-1-2").val();
		var ans_3 = $("#q-1-3").val();
		var ans_4 = $("#q-1-4").val();
		var ans_5 = $("#q-1-5").val();
		var ans_6 = $("#q-1-6").val();
		var ans_7 = $("#q-1-7").val();
		var ans_8 = $("#q-1-8").val();
		var ans_9 = $("#q-1-9").val();
		var ans_10 = $("#q-1-10").val();
				
		ans_1 = $.trim(ans_1);
		ans_2 = $.trim(ans_2);
		ans_3 = $.trim(ans_3);
		ans_4 = $.trim(ans_4);
		ans_5 = $.trim(ans_5);
		ans_6 = $.trim(ans_6);
		ans_7 = $.trim(ans_7);
		ans_8 = $.trim(ans_8);
		ans_9 = $.trim(ans_9);
		ans_10 = $.trim(ans_10);
		
		if ( $.inArray(ans_1,ans1 ) > "-1" ) {
			$("#img1").attr("src","img/tick.png");
		} else {
			$("#img1").attr("src","img/cross.png");
		}

		if ( $.inArray(ans_2,ans2 ) > "-1" ) {
			$("#img2").attr("src","img/tick.png");
		} else {
			$("#img2").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_3,ans3 ) > "-1" ) {
			$("#img3").attr("src","img/tick.png");
		} else {
			$("#img3").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_4,ans4 ) > "-1" ) {
			$("#img4").attr("src","img/tick.png");
		} else {
			$("#img4").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_5,ans5 ) > "-1" ) {
			$("#img5").attr("src","img/tick.png");
		} else {
			$("#img5").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_6,ans6 ) > "-1" ) {
			$("#img6").attr("src","img/tick.png");
		} else {
			$("#img6").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_7,ans7 ) > "-1" ) {
			$("#img7").attr("src","img/tick.png");
		} else {
			$("#img7").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_8,ans8 ) > "-1" ) {
			$("#img8").attr("src","img/tick.png");
		} else {
			$("#img8").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_9,ans9 ) > "-1" ) {
			$("#img9").attr("src","img/tick.png");
		} else {
			$("#img9").attr("src","img/cross.png");
		}
		if ( $.inArray(ans_10,ans10 ) > "-1" ) {
			$("#img10").attr("src","img/tick.png");
		} else {
			$("#img10").attr("src","img/cross.png");
		}
		$("img.tick").show();
		
		learningData.get(key, function(err, result){
			if (result != null) {
				result.q1_1 = $("#q-1-1").val();
				result.q1_2 = $("#q-1-2").val();
				result.q1_3 = $("#q-1-3").val();
				result.q1_4 = $("#q-1-4").val();
				result.q1_5 = $("#q-1-5").val();
				result.q1_6 = $("#q-1-6").val();
				result.q1_7 = $("#q-1-7").val();
				result.q1_8 = $("#q-1-8").val();
				result.q1_9 = $("#q-1-9").val();
				result.q1_10 = $("#q-1-10").val();
				
				learningData.set(key, result, function(err){
					// handle saving
				});

			}
		});

	});

});

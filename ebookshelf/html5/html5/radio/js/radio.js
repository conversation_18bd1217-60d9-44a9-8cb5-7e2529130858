$( document ).ready(function() {
	
	$("#ans-btn").hide();
	
	var student = $( "#dialogHtml5", window.parent.document ).attr('data-student');
	
	// get result
	$.ajax({
		async: false,
		dataType: "json",
		type: 'POST',
		url: '/ebookshelf/load/getResult.php',
		data: {
			'book_id': book_id,
			'page': page,
			'number': number,
			'student': student,
		},
		beforeSend: function() {
			$("div.warning").text("資訊同步中...");
		},
		success: function(response) {
			if (response.done) {
				// display answer
				if ( bookLang == 'eng' ) {
					$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
				} else {
					$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
				}
				
				$.each(qArray, function( index, value ) {
					$('input[name=' + qArray[index]['name'] + ']').filter('[value="' + response.result[index] + '"]').prop('checked', true);
				});
				
				$("input[type=radio]").attr('disabled', true);
				
			} else {
				
				if (response.type==2) {
					$("#ans-btn").show();
					if ( bookLang == 'eng' ) {
						$("div.warning").text("This test can only be submitted once. Be cautious.");
					} else {
						$("div.warning").text("本測驗只能提交一次，請小心使用。");
					}
				} else {
					if ( bookLang == 'eng' ) {
						$("#ans-btn").text('View Results').show();
					} else {
						$("#ans-btn").text('測驗結果').show();
					}
					$("div.warning").text("");
				}

			}
		},
		error: function (xhr,status,error) {
			$("div.warning").text("同步失敗，請再嘗試。");
		}
	});

	$("#ans-btn").click(function() {

		if ( $("#ans-btn").text()=="提交" || $("#ans-btn").text()=="Submit" ) {
			
			$("#ans-btn").hide();
		
			var mark = 0;
			var result = [];
			var total = 0;
			
			$.each(qArray, function( index, value ) {
				var ans1 = $('input[name=' + qArray[index]['name'] + ']:checked').val();
				result.push(ans1);
				total++;
				if ( ans1 == qArray[index]['ans'] ) mark++;
			});

			// save result
			$.ajax({
				// async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/saveResult.php',
				data: {
					'book_id': book_id,
					'page': page,
					'number': number,
					'result': result,
					'mark': mark,
					'total': total,
				},
				beforeSend: function() {
					$("div.warning").text("資料同步中.....");
				},
				success: function(response) {
					if (response.done) {
						// display answer
						$("#ans-btn").hide();
						if ( bookLang == 'eng' ) {
							$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
						} else {
							$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
						}
						
						$("input[type=radio]").attr('disabled', true);
						
					} else {
						$("#ans-btn").show();
						$("div.warning").text("同步失敗，請再嘗試。");
					}
				}
			});
		} else if ( $("#ans-btn").text()=="測驗結果" || $("#ans-btn").text()=="View Results" ) {
			var src = window.parent.$('#myHtml5').attr('src');
			window.parent.openTestDialog2(src,page,number);
		}
	});
	
	// old codes
	
	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	
	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );

	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});
	
	var audio = new Audio();
	
	audio.onended = function(){
		$('.track-btn').addClass('disable');
	};
	
	$('.track-btn').on('click', function() {
		if ( $(this).hasClass('disable') ) {
			var src = 'mp3/' + $(this).attr('data-mp3');
			$('.track-btn').addClass('disable');
			$(this).removeClass('disable');
			audio.src = src;
			audio.play();
		} else {
			$('.track-btn').addClass('disable');
			audio.pause();
		}
	});
	
});


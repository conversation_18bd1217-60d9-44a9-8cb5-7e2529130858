﻿* {
	font-family: DFKai-sb;
	font-size: 30px;
}

html{
	margin: 0;
	padding:0;
	font-family: <PERSON><PERSON>, "微軟正黑體", "Microsoft JhengHei";
	font-size: 18px;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

body {
	margin: 0;
	padding:0;
	font-family: <PERSON><PERSON>, "微軟正黑體", "Microsoft JhengHei";
	font-size: 18px;
	width: 800px;
	height: 600px;
	overflow: hidden;
	transform: scale(1,1);
	transform-origin: 0% 0% 0;
}

img {
	vertical-align: middle;
}

#main {
	width: 760px;
	height: 600px;
	margin: auto;
	font-size: 20px;
	padding-left: 20px;
	padding-right: 20px;
}

.page {
	display: none;
	position: absolute;
	width: 760px;
}

.track-btn {
    opacity: 1;
    filter: alpha(opacity=100); /* msie */
	cursor: pointer;
	vertical-align: middle;
}

.track-btn.disable {
    opacity: 0.4;
    filter: alpha(opacity=40); /* msie */
	cursor: pointer;
}

#popup{
	display:none;
	text-align:center;
	position:absolute;
	width:340px;
	height:180px;
	top:200px;
	left:200px;
	padding:30px;
	background-color: skyblue;
	border: 4px solid darkblue;
	border-radius: 25px;
}
table.main {
	padding: 0;
	width: 100%;
	border-collapse: collapse;
}

table.main th {
	vertical-align: middle;
	text-align: center;
	background-color: #bbdbc5;
}

table.main td {
	vertical-align: top;
	text-align: left;
}

.mc_answer {
	font-size: 50px;
    border-radius: 15px;
    border: 4px solid #76b064;
	height: 75px;
	line-height: 75px;
	width: 75px;
	position: absolute;
	right: 40px;
	bottom: 40px;
	background: #FFFFEE;
	text-align:center;
	opacity: 1.0;
}

.mc-answer-active {
	background: #FCDFFF;
}

.match-active {
	background-color: rgba(252, 223, 255, 0.5);
}

.mc-tick {
	position: absolute;
	right: -10px;
	bottom: 20px;
	display: none;
}

.draggable {
	font-size: 32px;
    border-radius: 10px;
    border: 2px solid #76b064;
	height: 40px;
	line-height: 40px;
	width: 40px;
	background: #FFFFEE;
	text-align:center;
	float: left;
	margin-right: 10px;
	cursor: -webkit-grab;
	cursor:-moz-grab;
}

.myButton {
	-moz-box-shadow:inset 0px 1px 0px 0px #f9eca0;
	-webkit-box-shadow:inset 0px 1px 0px 0px #f9eca0;
	box-shadow:inset 0px 1px 0px 0px #f9eca0;
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #f0c911), color-stop(1, #f2ab1e));
	background:-moz-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-webkit-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-o-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:-ms-linear-gradient(top, #f0c911 5%, #f2ab1e 100%);
	background:linear-gradient(to bottom, #f0c911 5%, #f2ab1e 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f0c911', endColorstr='#f2ab1e',GradientType=0);
	background-color:#f0c911;
	-moz-border-radius:6px;
	-webkit-border-radius:6px;
	border-radius:6px;
	border:1px solid #e65f44;
	display:inline-block;
	cursor:pointer;
	color:#c92200;
	font-family:Arial;
	font-size:28px;
	font-weight:bold;
	padding:10px 24px;
	text-decoration:none;
	text-shadow:0px 1px 0px #ded17c;
}
.myButton:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #f2ab1e), color-stop(1, #f0c911));
	background:-moz-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-webkit-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-o-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:-ms-linear-gradient(top, #f2ab1e 5%, #f0c911 100%);
	background:linear-gradient(to bottom, #f2ab1e 5%, #f0c911 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2ab1e', endColorstr='#f0c911',GradientType=0);
	background-color:#f2ab1e;
}

.myButton:active {
	position:relative;
	top:1px;	
}
.myButton:focus {
	outline: none;
}

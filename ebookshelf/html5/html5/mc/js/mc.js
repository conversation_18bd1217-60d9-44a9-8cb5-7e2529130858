$( document ).ready(function() {
	
	$("#ans-btn").hide();
	
	var student = $( "#dialogHtml5", window.parent.document ).attr('data-student');

	// get result
	$.ajax({
		async: false,
		dataType: "json",
		type: 'POST',
		url: '/ebookshelf/load/getResult.php',
		data: {
			'book_id': book_id,
			'page': page,
			'number': number,
			'student': student,
		},
		beforeSend: function() {
			$("div.warning").text("資訊同步中...");
		},
		success: function(response) {
			if (response.done) {
				// display answer
				if ( bookLang == 'eng' ) {
					$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
				} else {
					$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
				}
				
				var i = 0;
				$('div.mc_answer').each(function() {
					$(this).text( response.result[i] );
					i++;
				});
										
			} else {
				
				if (response.type==2) {
					$("#ans-btn").show();
					if ( bookLang == 'eng' ) {
						$("div.warning").text("This test can only be submitted once. Be cautious.");
					} else {
						$("div.warning").text("本測驗只能提交一次，請小心使用。");
					}
				} else {
					if ( bookLang == 'eng' ) {
						$("#ans-btn").text('View Results').show();
					} else {
						$("#ans-btn").text('測驗結果').show();
					}
					$("div.warning").text("");
					
					$("#ans-btn").insertAfter('button.nextButton:first');
					
				}
				
				$(".draggable").draggable({
					revert: "invalid",
					appendTo: "body",
					helper: "clone",
					containment: "window",
					start: function(event, ui) {
						ui.position.left = 0;
						ui.position.top = 0;
					},
					drag: function(event, ui) {

						var changeLeft = ui.position.left - ui.originalPosition.left; // find change in left
						var newLeft = ui.originalPosition.left + changeLeft / (( zoomLevel)); // adjust new left by our zoomScale

						var changeTop = ui.position.top - ui.originalPosition.top; // find change in top
						var newTop = ui.originalPosition.top + changeTop / zoomLevel; // adjust new top by our zoomScale

						ui.position.left = newLeft;
						ui.position.top = newTop;

					}
				});

				$(".mc_answer").droppable({
					hoverClass: "mc-answer-active",
					drop: function( event, ui ) {

						$(this).text(ui.draggable.attr('value'));

						setTimeout(function(){
							nextPage();
						}, 400);
					}
				});
				
			}
		},
		error: function (xhr,status,error) {
			$("div.warning").text("同步失敗，請再嘗試。");
		}
	});

	$('.nextButton').on('click', function() {
		nextPage();
	});

	$('.lastButton').on('click', function() {
		lastPage();
	});
	
	function nextPage() {
	
		$('.track-btn').addClass('disable');
		audio.pause();

		var pageNumber = $('.page:visible').attr('id');
		pageNumber = pageNumber.replace('page','');
		
		if ( pageNumber == pageTotal ) {
			// do nothing
		} else {
			$('#page' + pageNumber ).delay(100).hide( "slide", {direction:"left", distance:"100%", easing:"easeInOutCubic"}, 1000);
			$('#page' + ( parseInt(pageNumber) + 1 ) ).delay(100).show( "slide", {direction:"right", distance:"100%", easing:"easeInOutCubic"}, 1000, function() {
				
			});
		}
		
	}

	function lastPage() {
	
		$('.track-btn').addClass('disable');
		audio.pause();

		var pageNumber = $('.page:visible').attr('id');
		pageNumber = pageNumber.replace('page','');
		
		if ( pageNumber == 1 ) {
			// do nothing
		} else {
			$('#page' + pageNumber ).delay(100).hide( "slide", {direction:"right", distance:"100%", easing:"easeInOutCubic"}, 1000);
			$('#page' + ( parseInt(pageNumber) - 1 ) ).delay(100).show( "slide", {direction:"left", distance:"100%", easing:"easeInOutCubic"}, 1000, function() {
				
			});					
		}
		
	}
	
	
	$("#ans-btn").click(function() {
		
		if ( $("#ans-btn").text()=="提交" || $("#ans-btn").text()=="Submit" ) {
			var mark = 0;
			var result = [];
			var questions = [];
			
			$('div.mc_answer').each(function() {
				result.push( $(this).text() );
				if ( $(this).attr('value') == $(this).text() ) {
					mark++;
				}
			});
			
			// save result
			$.ajax({
				async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/saveResult.php',
				data: {
					'book_id': book_id,
					'page': page,
					'number': number,
					'result': result,
					'mark': mark,
					'total': pageTotal,
				},
				beforeSend: function() {
					$("div.warning").text("資料同步中.....");
				},
				success: function(response) {
					if (response.done) {
						// display answer
						$("#ans-btn").hide();
						if ( bookLang == 'eng' ) {
							$("div.warning").text("This test was submitted on " + response.submit2 + ". Thank you.");
						} else {
							$("div.warning").text("測驗已於" + response.submit + "提交。謝謝。");
						}
						
						$(".draggable").draggable({ disabled: true });
						
					} else {
						$("#ans-btn").show();
						
					}
				}
			});
		} else if ( $("#ans-btn").text()=="測驗結果" || $("#ans-btn").text()=="View Results" ) {
			var src = window.parent.$('#myHtml5').attr('src');
			window.parent.openTestDialog2(src,page,number);
		}

	});	

	// old codes

	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	
	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );

	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});
	
	$('#page1').show();
	
	var audio = new Audio();
	
	audio.onended = function(){
		$('.track-btn').addClass('disable');
	};
	
	$('.track-btn').on('click', function() {
		if ( $(this).hasClass('disable') ) {
			var src = 'mp3/' + $(this).attr('data-mp3');
			$('.track-btn').addClass('disable');
			$(this).removeClass('disable');
			audio.src = src;
			audio.play();
		} else {
			$('.track-btn').addClass('disable');
			audio.pause();
		}
	});
	
	
	
});	


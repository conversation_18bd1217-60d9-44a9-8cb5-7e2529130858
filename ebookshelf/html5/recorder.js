$( document ).ready(function() {

	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	
	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );

	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});

	var myInterval;
	var vid = document.getElementById("myAudio");
	$('.finger').hide();

	$('#play-btn').click(function(){
		if ( !$(this).hasClass('playing') ) {
			$('#score-table').find('td').css('background-color','black');
			vid.currentTime = 0;
			vid.play();
		}
	});

	$('#stop-btn').click(function(){
		vid.pause();
		vid.currentTime = 0;
	});

	$('#myAudio').bind('pause',function(){
		clearInterval(myInterval);
		$('#play-btn').removeClass('playing');
		$('#score-table').find('td').css('background-color','black');
		$('.finger').hide();
	});

	$('#myAudio').bind('play',function(){
		$('#play-btn').addClass('playing');
		myInterval = setInterval( playRecorder, 100);

	});


});

function redCell(i) {
	$('#score-table').find('td').css('background-color','black');
	$('#score-table').find('td:eq(' + i + ')').css('background-color','red');
}


function finger(note) {
	$('.finger').hide();
	switch(note) {
		case 'C3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-7').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();			
			break;
		case 'CS3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-7').addClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'D3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'DS3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').addClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'E3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'F3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-7').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'FS3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'G3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'GS3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').addClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'A3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'AS3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			// $('#finger-6').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'B3':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'C4':
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-8').removeClass('fingerHalf').show();
			break;
		case 'CS4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			break;
		case 'D4':
			$('#finger-2').removeClass('fingerHalf').show();
			break;
		case 'DS4':
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			break;
		case 'E4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'F4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'FS4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'G4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-3').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'GS4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'A4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'AS4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-6').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
		case 'B4':
			$('#finger-1').removeClass('fingerHalf').show();
			$('#finger-2').removeClass('fingerHalf').show();
			$('#finger-4').removeClass('fingerHalf').show();
			$('#finger-5').removeClass('fingerHalf').show();
			$('#finger-8').addClass('fingerHalf').show();
			break;
	}
}
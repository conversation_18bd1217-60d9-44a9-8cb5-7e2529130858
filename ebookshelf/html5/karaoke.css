html {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
body {
	margin: 0;
	padding: 0;
	width: 800px;
	height: 600px;
	overflow: hidden;
	transform: scale(1,1);
	transform-origin: 0% 0% 0;
}
#main {
	width: 800px;
	height: 600px;
	margin: 0 auto;
	overflow: hidden;
	position: relative;
	z-index: -1;
}
*:focus {
    outline: none;
}
.button {
	position: absolute;
	border: none;
	background: none;
	background-size: contain;
	cursor: pointer;
	
}
.button:active{
	margin-top: 1px;
}
#play-start-btn {
	width: 129px;
	height: 71px;
	top: 410px;
	left: 334px;
	background-image: url(karaoke/remote-control-1.png);
}
#play-btn {
	width: 129px;
	height: 71px;
	top: 410px;
	left: 284px;
}
#play-btn.playing {
	background-image: url(karaoke/remote-control-2.png);
}
#play-btn.paused {
	background-image: url(karaoke/remote-control-3.png);
}
#demo-btn {
	width: 77px;
	height: 97px;
	top: 383px;
	left: 434px;
}
#demo-btn.demo {
	background-image: url(karaoke/demo.png);
}
#demo-btn.accomp {
	background-image: url(karaoke/accomp.png);
}
#vid2 {
	z-index: -1;
}
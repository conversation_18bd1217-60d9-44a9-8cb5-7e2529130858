html {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
body {
	margin: 0;
	padding: 0;
	width: 800px;
	height: 600px;
	overflow: hidden;
	transform: scale(1,1);
	transform-origin: 0% 0% 0;
}
#main {
	width: 800px;
	height: 600px;
	margin: 0 auto;
	overflow: hidden;
	background-image: url(recorder/bg.png);
}
table {
	border-collapse: collapse;
	table-layout:fixed;
}
#main-table {
	width: 100%;
}
#main-table td {
	text-align: center;
}
#play-btn{
	width:84px;
	height:26px;
	position:absolute;
	top:350px;
	left:250px;
	border:none;
	background:none;
	background-image:url('recorder/play-1.png');
	cursor:pointer;
}
#play-btn:active{
	top:351px;
	left:251px;
}
#play-btn.playing{
	background-image:url('recorder/play-2.png');
	cursor:auto;
}
#play-btn.playing:active{
	top:350px;
	left:250px;
}
#stop-btn{
	width:84px;
	height:26px;
	position:absolute;
	top:350px;
	left:350px;
	border:none;
	background:none;
	background-image:url('recorder/stop-1.png');
	cursor:pointer;
}
#stop-btn:active{
	top:351px;
	left:351px;
}
#stop-btn:hover{
	background-image:url('recorder/stop-2.png');
}
*:focus {
    outline: none;
}
#recorder {
	position:absolute;
	top:10px;
	left:10px;
	width:70px;
	height:550px;
	background:none;
	background-image:url('recorder/recorder.png');
	background-size: 100% 100%;
}
.finger {
	position:absolute;
	width:30px;
	height:30px;
	background:none;
	background-image:url('recorder/finger.png');
	background-size: 100% 100%;
}

.fingerHalf {
	background-image:url('recorder/finger2.png');
}
#finger-1 {
	top:218px;
	left:10px;
}
#finger-2 {
	top:253px;
	left:10px;
}
#finger-3 {
	top:290px;
	left:10px;
}
#finger-4 {
	top:333px;
	left:10px;
}
#finger-5 {
	top:370px;
	left:10px;
}
#finger-6 {
	top:418px;
	left:18px;
}
#finger-7 {
	top:458px;
	left:18px;
}
#finger-8 {
	top:218px;
	left:48px;
}

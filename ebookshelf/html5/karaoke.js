$( document ).ready(function() {
	
	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});

	var ua = navigator.userAgent.toLowerCase();
	var isAndroid = ua.indexOf("android") > -1; //&& ua.indexOf("mobile");

	var zoomLevel1 = $( window ).width() / $( 'body' ).width() ;
	var zoomLevel2 = $( window ).height() / $( 'body' ).height() ;
	var zoomLevel = Math.min( zoomLevel1, zoomLevel2 );
	
	$( 'body' ).css({
		'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
	});

	// vid1 = audio
	var vid1 = document.getElementById("vid1");
	vid1.muted = true;
	// vid2 = video
	var vid2 = document.getElementById("vid2");
	
	resetAll();
	
	$('#vid1').bind('ended',function(){
		resetAll();
	});
	$('#vid2').bind('ended',function(){
		resetAll();
	});
	
	$('#vid2').bind('playing',function(){
		vid1.play();
	});
	
	$('#vid1').bind('playing',function(){
		var addTime;
		if(isAndroid) {
			// do nothing
		} else {
			addTime = 0.3 * vid2.currentTime / vid2.duration;
			vid2.currentTime = vid1.currentTime + addTime;
		}
		console.log(addTime);
	});
	
	
	$('#vid2').bind('pause',function(){
		vid1.pause();
	});
	
	$('#play-start-btn').click(function(){
		
		vid2.play();
		
		vid1.muted = false;
		vid2.muted = false;
		
		setTimeout(function(){ vid1.muted=true; }, 1000);

		$('#play-btn').show();
		$('#demo-btn').show();
		$('#play-start-btn').hide();		
		
	});

	$('#play-btn').click(function(){
		if ( $('#play-btn').hasClass('playing') ) {
			vid2.pause();
			$('#play-btn').removeClass('playing').addClass('paused');
		} else {
			vid2.play();
			$('#play-btn').removeClass('paused').addClass('playing');
		}
	});
    
	$('#demo-btn').click(function(){
		if ( $('#demo-btn').hasClass('demo') ) {
			$('#demo-btn').removeClass('demo').addClass('accomp');
			vid1.muted = false;
			vid2.muted = true;
		} else {
			$('#demo-btn').removeClass('accomp').addClass('demo');
			vid1.muted = true;
			vid2.muted = false;
		}
	});
	
	function resetAll() {
		vid1.pause();
		vid2.pause();
		vid1.load();
		vid2.load();
		$('#play-start-btn').show();
		$('#play-btn').removeClass('paused').addClass('playing').hide();
		$('#demo-btn').removeClass('accomp').addClass('demo').hide();	
	}
	
});

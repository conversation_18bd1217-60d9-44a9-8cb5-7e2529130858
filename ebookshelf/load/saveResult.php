<?php

 error_reporting(E_ALL);
 ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->done = false;


if(isset($_SESSION['user_session'])) {
	
	if( isset($_POST['book_id']) && isset($_POST['page']) && isset($_POST['number']) && isset($_POST['mark']) && isset($_POST['result']) && isset($_POST['total']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);
		$page = trim($_POST['page']);
		$number = trim($_POST['number']);
		$mark = trim($_POST['mark']);
		$total = trim($_POST['total']);
		$result = serialize( $_POST['result'] );

		openConnection();
		
		// $result = $objConn->real_escape_string($result);

		// $SQL = "SELECT * FROM student_result WHERE student_id=".$_SESSION['user_session']." AND book_id=$book_id AND page=$page AND number=$number AND deleted=0 ";
		$SQL = "INSERT INTO student_result (total_mark,book_id,page,number,test_mark,test_result,teacher_id,student_id,create_date,class_id) VALUE ('$total','$book_id','$page','$number','$mark','$result','".$_SESSION['teacher_id']."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."','".$_SESSION['class_id']."')";
		
		if ( $objConn->query($SQL) === TRUE) {
			$response->done = true;
			$response->id = $objConn->insert_id;
			$date = date_create();
			$response->submit = date_format($date,'Y年m月d日H時i分');
			$response->submit2 = date_format($date,'Y-m-d \a\t H:i');
		} else {
			$response->error = $objConn->error;
			$response->sql = $SQL;
		}
		
		closeConnection();
	}
}

echo json_encode($response);

?>
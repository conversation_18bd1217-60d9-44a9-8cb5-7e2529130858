<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;

if(isset($_SESSION['user_session'])) {
	
	if( isset($_POST['cid']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		openConnection();
		
		$custom_id = trim($_POST['cid']);

		if ( $_SESSION['type'] == 1 ) {
			$SQL = "UPDATE ebook_custom SET ";
			$SQL .= "disabled=1,";
			$SQL .= "edit_user='".$_SESSION['user_session']."',";
			$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
			$SQL .= "WHERE custom_id=$custom_id";
		} else {
			$SQL = "UPDATE student_custom SET ";
			$SQL .= "disabled=1,";
			$SQL .= "edit_user='".$_SESSION['user_session']."',";
			$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
			$SQL .= "WHERE custom_id=$custom_id";			
		}
		
		if ( $objConn->query($SQL) ) {
			
			if ( $_SESSION['type'] == 1 ) {
				$SQL2 = "SELECT * FROM ebook_custom WHERE custom_id=$custom_id";
			} else {
				$SQL2 = "SELECT * FROM student_custom WHERE custom_id=$custom_id";
			}
			
			if ($result2 = $objConn->query($SQL2)) {
				if ( $result2->num_rows > 0 ) {
					$row2 = $result2->fetch_assoc();
					$page = $row2['page'];
					$class = $row2['class'];
					$book_id = $row2['book_id'];
					
					$response->success = true;
					$response->cid = 0;
					$response->page = $page;
					
					if ( $_SESSION['type'] == 1 ) {
						$SQLOG = "INSERT INTO ebook_log (log,page,class,book_id,teacher_id,edit_date,path,teacher_login) VALUES ";
						$SQLOG .= "('delete custom note successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."','','')";
					} else {
						$SQLOG = "INSERT INTO student_log (log,page,class,book_id,student_id,edit_date) VALUES ";
						$SQLOG .= "('delete custom note successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
					}
					$objConn->query($SQLOG);
				}
			}

		}
		
		closeConnection();

	}
}

echo json_encode($response);


?>
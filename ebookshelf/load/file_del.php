<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;

if(isset($_SESSION['user_session'])) {
	
	if( isset($_POST['file_id']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		openConnection();
		
		$file_id = trim($_POST['file_id']);

		$SQL = "UPDATE ebook_file SET ";
		$SQL .= "disabled=1,";
		$SQL .= "edit_user='".$_SESSION['user_session']."',";
		$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
		$SQL .= "WHERE file_id=$file_id AND teacher_id=".$_SESSION['user_session']." ";
		
		if ( $objConn->query($SQL) ) {
			
			$SQL2 = "SELECT * FROM ebook_file WHERE file_id=$file_id AND teacher_id=".$_SESSION['user_session']." ";
			if ($result2 = $objConn->query($SQL2)) {
				if ( $result2->num_rows > 0 ) {
					$row2 = $result2->fetch_assoc();
					$file_path = $row2['file_path'];
					
					unlink( $file_path );
					
					$response->success = true;
					$response->file_id = $file_id;
					
					$SQL3 = "UPDATE ebook_custom SET data=replace(data,'[#file".$file_id."]','') WHERE data like '%[#file".$file_id."]%'";
					$objConn->query($SQL3);
					
					$SQLOG = "INSERT INTO ebook_log (log,class,teacher_id,edit_date,book_id,page,path,teacher_login) VALUES ";
					$SQLOG .= "('delete file successfully. (".$file_id.")','file','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."',0,0,'','')";
					$objConn->query($SQLOG);
				}
			}

		}
		
		closeConnection();

	}
}

echo json_encode($response);


?>
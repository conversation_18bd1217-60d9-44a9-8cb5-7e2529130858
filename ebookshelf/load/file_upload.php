<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;
$response->message = "";

if( isset($_SESSION['user_session']) ) {

	// print_r($_FILES);
	$fileName = $_FILES['file']['name'];
	$fileType = $_FILES['file']['type'];
	$fileError = $_FILES['file']['error'];
	// $fileContent = file_get_contents($_FILES['file']['tmp_name']);

	if($fileError == UPLOAD_ERR_OK){
	   //Processes your file here
		$save_path = "files/";
		// $save_name = microtime(). strrchr(strtolower($_FILES['file']['name']), '.');
		$save_name = uniqid(rand(),true) . strrchr(strtolower($_FILES['file']['name']), '.');

		$save_pathname = $save_path.$save_name;

		if(move_uploaded_file($_FILES['file']['tmp_name'],$save_pathname)) {
			// Other codes
			$response->message = 'file upload success.';
			require_once '../inc/settings.inc.php';
			require_once '../inc/functions.inc.php';

			openConnection();

			$SQL = "INSERT INTO ebook_file (teacher_id,file_path,file_name,file_type,disabled,edit_user,edit_date) ";
			$SQL .= "VALUES ('".$_SESSION['user_session']."','".$save_pathname."','".$fileName."','".$fileType."','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";

			if ( $objConn->query($SQL) ) {
				$response->success = true;
				$response->new_id = $objConn->insert_id;

				$SQLOG = "INSERT INTO ebook_log (log,class,teacher_id,edit_date,book_id,page,path,teacher_login) VALUES ";
				$SQLOG .= "('upload new file successfully. (".$objConn->insert_id.")','file','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."',0,0,'','')";
				$objConn->query($SQLOG);

			}

			closeConnection();

		}


	}else{
	   switch($fileError){
		 case UPLOAD_ERR_INI_SIZE:
			  $message = 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
			  break;
		 case UPLOAD_ERR_FORM_SIZE:
			  $message = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
			  break;
		 case UPLOAD_ERR_PARTIAL:
			  $message = 'The uploaded file was only partially uploaded.';
			  break;
		 case UPLOAD_ERR_NO_FILE:
			  $message = 'No file was uploaded.';
			  break;
		 case UPLOAD_ERR_NO_TMP_DIR:
			  $message = 'Missing a temporary folder.';
			  break;
		 case UPLOAD_ERR_CANT_WRITE:
			  $message= 'Failed to write file to disk.';
			  break;
		 case  UPLOAD_ERR_EXTENSION:
			  $message = 'A PHP extension stopped the file upload.';
			  break;
		 default: $message = 'Other Error.';
				break;
		}
		$response->success = false;
		$response->message = $message;
	}

}

echo json_encode($response);

?>
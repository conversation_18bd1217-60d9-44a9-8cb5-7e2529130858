<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if( isset($_SESSION['user_session']) && isset($_SESSION['type']) && isset($_GET['bookid']) && isset($_GET['classid']) ) {
	
	require_once '../inc/settings.inc.php';
	require_once '../inc/functions.inc.php';

	$book_id = trim($_POST['bookid']);
	$class_id = trim($_POST['classid']);

	openConnection();
	
	if( $_SESSION['type'] == 1 ) {
		// teacher only
		
		/** PHPExcel */
		require_once '../phpclass/excel/PHPExcel.php';

		// Create new PHPExcel object 
		$objReader = new PHPExcel_Reader_Excel5();
		$objPHPExcel = $objReader->load("../xls/testCount.xls");

		//get data from database
		$SQL = "SELECT * FROM student_class WHERE class_id='".$class_id."' AND deleted=0 ";
		if ($result = $objConn->query($SQL)) {
			$i=0;
			while($row = $result->fetch_assoc()) {
				// $response->classes[$i]=$row;
				$books = explode(",",$row['books']);
				if ( in_array($book_id,$books) ) {
					// $response->teacher = true;
					// $response->classes[$i]=$row;
					// $response->classes[$i]['rows'] = array();
					
					$objPHPExcel->setActiveSheetIndex(0)
								->setCellValue('B1', $row['class_name']);
					
					$SQL2 = "SELECT book_id, page, number, count(test_id) AS count FROM student_result WHERE class_id='".$row['class_id']."' AND book_id='".$book_id."' AND deleted=0 GROUP BY book_id, page, number";
					if ($result2 = $objConn->query($SQL2)) {
						$row_cnt2 = $result2->num_rows;
						if ( $row_cnt2 > 0 ) {
							$j=0;
							while($row2 = $result2->fetch_assoc()) {
								// $response->classes[$i]['rows'][$j]=$row2;
								$j++;
							}
							$result2->close();
						}
					}
					$i++;
				}
			}
			$result->close();
		}
		
	}

}












require_once "../inc/settings.inc.php"; 
require_once "../inc/functions.inc.php"; 
include "../inc/check.user.php"; 

if (isset($_GET['formid'])){

$form_id = $_GET['formid'];
$condition1 = "expences_main.form_id='$form_id' AND expences_main.user='$user' ";
$condition2 = "expences_son.form_id='$form_id' AND expences_son.user='$user' ";
$companyid = 0;

/** Error reporting */
error_reporting(E_ALL);

date_default_timezone_set('Asia/Hong_Kong');

/** PHPExcel */
require_once '../phpclass/excel/PHPExcel.php';

// Create new PHPExcel object 
// $objReader = new PHPExcel_Reader_Excel5();
$objPHPExcel = = new PHPExcel();

//$objPHPExcel->getDefaultStyle()->getFont()->setSize(8);

//get data from database
$query = "SELECT expences_main.form_id, company.name, expences_main.edit_date, users.fullname, expences_main.company_id FROM expences_main, users, company WHERE expences_main.user=users.user AND expences_main.company_id=company.id AND $condition1 ";
$sql_result = mysql_query($query);

// write header info
while ($row2 = mysql_fetch_row($sql_result)) {
$companyid = $row2[4];
$objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', $row2[1])
            ->setCellValue('H5', $row2[2])
            ->setCellValue('B7', $row2[3])
            ->setCellValue('H7', $row2[0]);
}

// set password to protect worksheet
$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
$objPHPExcel->getActiveSheet()->getProtection()->setPassword('wrastupevUzuN7cANER2');

//get data from database
$query = "SELECT expences_son.receipt_date, expences_son.description, expences_son.office, expences_son.order, team.team_name, customer.customer_name, expences_son.log_no, expences_son.amt_rmb, expences_son.amt_hkd, expences_son.receipt FROM expences_son, team, customer WHERE expences_son.team=team.team_id AND expences_son.customer=customer.customer_id AND $condition2 ";
$sql_result = mysql_query($query);
            
//write data to excel
			$row_no=16;
			while ($row2 = mysql_fetch_row($sql_result)) {
				$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(0, $row_no)->setValue($row2[0]);
				$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(1, $row_no)->setValue($row2[1]);
				if ($companyid==1) {
					if ($row2[2]==0) {
						$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $row_no)->setValue("H");
					} else {
						$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $row_no)->setValue("S");
					}
					if ($row2[3]==0) {
						$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $row_no)->setValue("N");
					} else {
						$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $row_no)->setValue("O");
					}
					$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $row_no)->setValue($row2[4]);
					$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(5, $row_no)->setValue($row2[5]);
					$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(6, $row_no)->setValue($row2[6]);
				}
				if ($row2[7]>0) {
				$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(7, $row_no)->setValue($row2[7]);
				}
				if ($row2[8]>0) {
				$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(8, $row_no)->setValue($row2[8]);
				}
				if ($row2[9]==0) {
					$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(9, $row_no)->setValue("No");
				} else {
					$objPHPExcel->getActiveSheet()->getCellByColumnAndRow(9, $row_no)->setValue("Yes");
				}
				$row_no++;
			}

							 
// Rename sheet
$objPHPExcel->getActiveSheet()->setTitle(date("Y-m-d"));
//$row=$row+4;
//$objPHPExcel->getActiveSheet()->getPageSetup()->setPrintArea('A1:K'.$row); 

// Set active sheet index to the first sheet, so Excel opens this as the first sheet
$objPHPExcel->setActiveSheetIndex(0);

// insert work log
//mysql_query("INSERT INTO worklog (user_id,center,servicetype,table_name,action_type,action_detail,ip_address,cd) VALUES ('$user','$center2','$servicetype','assets','12','輸出到 Excel (標籤)','".$_SERVER['REMOTE_ADDR']."','".date("Y-m-d H:i:s")."')");


// Redirect output to a client’s web browser (Excel5)
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="ClaimForm_'.$form_id.'.xls"');
header('Cache-Control: max-age=0');

$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
$objWriter->save('php://output');
exit;

}
?>
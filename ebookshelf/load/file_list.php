<?php

 // error_reporting(E_ALL);
 // ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;

if(isset($_SESSION['user_session'])) {

	require_once '../inc/settings.inc.php';
	require_once '../inc/functions.inc.php';

	openConnection();

	$SQL = "SELECT * FROM ebook_file WHERE teacher_id=".$_SESSION['user_session']." AND disabled=0 ";

	if ($result = $objConn->query($SQL)) {
		$row_cnt = $result->num_rows;
		if ( $row_cnt > 0 ) {
			$i=0;
			while($row = $result->fetch_assoc()) {
				$response->files[$i] = $row;
				$i++;
			}

			$response->success = true;

			$SQLOG = "INSERT INTO ebook_log (log,teacher_id,edit_date,book_id,page,class,path,teacher_login) VALUES ";
			$SQLOG .= "('get file list successfully.','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."',0,0,'','','')";
			// $objConn->query($SQLOG);
		}
		$result->close();
	}

	closeConnection();

}

echo json_encode($response);

?>
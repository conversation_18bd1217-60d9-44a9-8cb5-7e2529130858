<?php

 // error_reporting(E_ALL);
 // ini_set('display_errors', 1);

session_start();

if(isset($_SESSION['user_session'])) {
	
	if( isset($_POST['book_id']) && isset($_POST['log']) && isset($_POST['page']) && isset($_POST['classname']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);
		$page = trim($_POST['page']);
		$class = trim($_POST['classname']);
		$log = trim($_POST['log']);
		

		openConnection();

		if ( $_SESSION['type'] == 1 ) {
			$SQLOG = "INSERT INTO ebook_log (log,class,page,book_id,teacher_id,edit_date,path,teacher_login) VALUES ";
			$SQLOG .= "('".$log."','".$class."','".$page."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."','','')";
			$objConn->query($SQLOG);
		} else {
			$SQLOG = "INSERT INTO student_log (log,class,page,book_id,student_id,edit_date) VALUES ";
			$SQLOG .= "('".$log."','".$class."','".$page."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			$objConn->query($SQLOG);
		}

		closeConnection();

	} else {
		echo "[]"; // empty json
	}
} else {
	echo "[]"; // empty json
}


?>
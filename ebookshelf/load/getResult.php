<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->done = false;
$response->type = $_SESSION['type'];

if(isset($_SESSION['user_session'])) {
	
	if( isset($_POST['book_id']) && isset($_POST['page']) && isset($_POST['number']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);
		$page = trim($_POST['page']);
		$number = trim($_POST['number']);

		openConnection();
		
		if ( isset($_POST['student']) && $_POST['student'] <> '' ) {
			
			$SQL = "SELECT * FROM student_result WHERE student_id='".$_POST['student']."' AND book_id=$book_id AND page=$page AND number=$number AND deleted=0 ";

			if ($result = $objConn->query($SQL)) {
				$row_cnt = $result->num_rows;
				if ( $row_cnt > 0 ) {
					$row = $result->fetch_assoc();
					$response->result = unserialize( $row['test_result'] );
					$response->mark = $row['test_mark'];
					$date = date_create($row['create_date']);
					$response->submit = date_format($date,'Y年m月d日H時i分');
					$response->submit2 = date_format($date,'Y-m-d \a\t H:i');
					$response->done = true;
					$result->close();
				}
			} else {
				$response->error = $objConn->error;
				$response->sql = $SQL;

			}
			
			
		} else {

			$SQL = "SELECT * FROM student_result WHERE student_id='".$_SESSION['user_session']."' AND book_id=$book_id AND page=$page AND number=$number AND deleted=0 ";

			if ($result = $objConn->query($SQL)) {
				$row_cnt = $result->num_rows;
				if ( $row_cnt > 0 ) {
					$row = $result->fetch_assoc();
					$response->result = unserialize( $row['test_result'] );
					$response->mark = $row['test_mark'];
					$date = date_create($row['create_date']);
					$response->submit = date_format($date,'Y年m月d日H時i分');
					$response->submit2 = date_format($date,'Y-m-d \a\t H:i');
					$response->done = true;
					$result->close();
				}
			} else {
				$response->error = $objConn->error;
				$response->sql = $SQL;

			}
			
		}
		
		closeConnection();
	}
}

echo json_encode($response);

?>
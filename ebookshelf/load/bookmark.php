<?php

 // error_reporting(E_ALL);
 // ini_set('display_errors', 1);

session_start();

if(isset($_SESSION['user_session'])) {
	
	if(isset($_POST['book_id'])) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);

		openConnection();
		
		if ( $_SESSION['type'] == 1 ) {
			$SQL = "SELECT pages FROM ebook_bookmark WHERE teacher_id='".$_SESSION['user_session']."' AND book_id=$book_id AND disabled=0 ";
		} else {
			$SQL = "SELECT pages FROM student_bookmark WHERE student_id='".$_SESSION['user_session']."' AND book_id=$book_id AND disabled=0 ";
		}

		if ($result = $objConn->query($SQL)) {
			$row_cnt = $result->num_rows;
			if ( $row_cnt > 0 ) {
				$row = $result->fetch_assoc();
		
				if ( $_SESSION['type'] == 1 ) {
					$SQLOG = "INSERT INTO ebook_log (log,book_id,teacher_id,edit_date,page,class,path,teacher_login) VALUES ";
					$SQLOG .= "('get bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."',0,'','','')";
				} else {
					$SQLOG = "INSERT INTO student_log (log,book_id,student_id,edit_date) VALUES ";
					$SQLOG .= "('get bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
				}
				// $objConn->query($SQLOG);

				echo $row['pages'];
			} else {
				echo "[]";
			}
			$result->close();
		} else {
			echo "[]"; // empty json
		}

		closeConnection();

	} else {
		echo "[]"; // empty json
	}
} else {
	echo "[]"; // empty json
}


?>
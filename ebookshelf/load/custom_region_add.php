<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;

if(isset($_SESSION['user_session'])) {

	if(isset($_POST['book_id']) && isset($_POST['page']) && isset($_POST['class']) && isset($_POST['x']) && isset($_POST['y']) && isset($_POST['width']) && isset($_POST['height']) ) {

		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';

		$book_id = trim($_POST['book_id']);
		$page = trim($_POST['page']);
		$class = trim($_POST['class']);
		$x = trim($_POST['x']);
		$y = trim($_POST['y']);
		$width = trim($_POST['width']);
		$height = trim($_POST['height']);

		openConnection();

		if ( $_SESSION['type'] == 1 ) {
			$SQL = "INSERT INTO ebook_custom (teacher_id,book_id,page,class,x,y,width,height,disabled,edit_user,edit_date) ";
			$SQL .= "VALUES ('".$_SESSION['user_session']."','$book_id','$page','$class','$x','$y','$width','$height','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
		} else {
			$SQL = "INSERT INTO student_custom (student_id,book_id,page,class,x,y,width,height,disabled,edit_user,edit_date) ";
			$SQL .= "VALUES ('".$_SESSION['user_session']."','$book_id','$page','$class','$x','$y','$width','$height','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";			
		}

		if ( $objConn->query($SQL) ) {
			$response->success = true;
			$response->new_id = $objConn->insert_id;


			if ( $_SESSION['type'] == 1 ) {
				$SQLOG = "INSERT INTO ebook_log (log,page,class,book_id,teacher_id,edit_date,path,teacher_login) VALUES ";
				$SQLOG .= "('create custom note successfully. (".$objConn->insert_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."','','')";
			} else {
				$SQLOG = "INSERT INTO student_log (log,page,class,book_id,student_id,edit_date) VALUES ";
				$SQLOG .= "('create custom note successfully. (".$objConn->insert_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			}
			$objConn->query($SQLOG);

		}

		closeConnection();

	}
}

echo json_encode($response);


?>
<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->student = false;
$response->teacher = false;
// $response->classes = array();
// $response->classes2 = new stdClass;

if( isset($_SESSION['user_session']) && isset($_SESSION['type']) && isset($_POST['book_id']) ) {

	require_once '../inc/settings.inc.php';
	require_once '../inc/functions.inc.php';

	$book_id = trim($_POST['book_id']);

	openConnection();
		
	if( $_SESSION['type'] == 1 ) {
		// teacher
		
		// get class
		$SQL = "SELECT * FROM student_class WHERE teacher_id='".$_SESSION['user_session']."' AND deleted=0 ";
		if ($result = $objConn->query($SQL)) {
			$i=0;
			while($row = $result->fetch_assoc()) {
				// $response->classes[$i]=$row;
				$books = explode(",",$row['books']);
				if ( in_array($book_id,$books) ) {
					$response->teacher = true;
					$response->classes[$i]=$row;
					$response->classes[$i]['rows'] = array();
					$SQL2 = "SELECT book_id, page, number, count(test_id) AS count FROM student_result WHERE class_id='".$row['class_id']."' AND book_id='".$book_id."' AND deleted=0 GROUP BY book_id, page, number";
					if ($result2 = $objConn->query($SQL2)) {
						$row_cnt2 = $result2->num_rows;
						if ( $row_cnt2 > 0 ) {
							$j=0;
							while($row2 = $result2->fetch_assoc()) {
								$response->classes[$i]['rows'][$j]=$row2;
								$j++;
							}
							$result2->close();
						}
					}
					$i++;
				}
			}
			$result->close();
		} else {
			$response->error = $objConn->error;
			// $response->sql = $SQL;
		}
	
		
	} elseif( $_SESSION['type'] == 2 ) {
		// student
		$SQL = "SELECT test_id, book_id, page, number, test_mark, total_mark, teacher_id, create_date, class_id FROM student_result WHERE student_id='".$_SESSION['user_session']."' AND book_id=$book_id AND deleted=0 ";

		if ($result = $objConn->query($SQL)) {
			$row_cnt = $result->num_rows;
			if ( $row_cnt > 0 ) {
				$i=0;
				while($row = $result->fetch_assoc()) {
					$response->rows[$i]=$row;
					$i++;
				}
				$result->close();
				// $row = $result->fetch_assoc();
				// $response->result[$i] = unserialize( $row['test_result'] );
				// $response->mark = $row['test_mark'];
				// $date = date_create($row['create_date']);
				// $response->submit = date_format($date,'Y年m月d日H時i分');
				$response->student = true;
			}
		} else {
			$response->error = $objConn->error;
			// $response->sql = $SQL;
		}

	}
	
	closeConnection();
	
}

echo json_encode($response);

?>
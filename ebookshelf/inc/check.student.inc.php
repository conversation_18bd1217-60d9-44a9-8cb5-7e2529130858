<?php
require_once 'settings.inc.php';
require_once 'functions.inc.php';

$actual_link = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

openConnection();
$SQL = "SELECT student_id FROM student WHERE student_id='".$_SESSION['user_session']."'";
if ($result = $objConn->query($SQL)) {
	$row_cnt = $result->num_rows;
	if ( $row_cnt == 1 ) {
		$row = $result->fetch_assoc();
		$user_name = $row['student_id'];
		
		$SQLOG = "INSERT INTO student_log (log,path,student_id,student_login,edit_date) VALUES ";
		$SQLOG .= "('user check successfully.','".$actual_link."','".$_SESSION['user_session']."','".$_SESSION['user_name']."','".date("Y-m-d H:i:s")."')";
		// $objConn->query($SQLOG);

	} else {
		
		$SQLOG = "INSERT INTO student_log (log,path,student_id,student_login,edit_date) VALUES ";
		$SQLOG .= "('user check error: 1','".$actual_link."','".$_SESSION['user_session']."','".$_SESSION['user_name']."','".date("Y-m-d H:i:s")."')";
		$objConn->query($SQLOG);

		session_unset();
		if(session_destroy()) {
			header("Location: /ebookshelf/login.php");
		}
	}
	$result->close();
} else {
	
	$SQLOG = "INSERT INTO student_log (log,path,student_id,student_login,edit_date) VALUES ";
	$SQLOG .= "('user check error: 2','".$actual_link."','".$_SESSION['user_session']."','".$_SESSION['user_name']."','".date("Y-m-d H:i:s")."')";
	$objConn->query($SQLOG);

	session_unset();
	if(session_destroy()) {
		header("Location: /ebookshelf/login.php");
	}
}
closeConnection();


?>

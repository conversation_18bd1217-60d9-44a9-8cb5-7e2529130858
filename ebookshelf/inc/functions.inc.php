<?php 
	
	$objConn = false;
	
	function openConnection() {
		global $objConn;
		$objConn = new mysqli("localhost", constant("DATABASEUSERNAME"), constant("DATABASEPASSWORD"),constant("DATABASENAME"));
		if ($objConn->connect_errno) {
			printf("Connect failed: %s\n", $objConn->connect_error);
			exit();
		}
		if (!$objConn->set_charset("utf8")) {
			printf("Error loading character set utf8: %s\n", $objConn->error);
		}
	}
	
	function closeConnection() {
		global $objConn;
		$objConn->close();
	}

	function getMenu($query) {
		global $objConn;
		if ($result = $objConn->query($query)) {
			$i=0;
			while($row = $result->fetch_array()) {
				echo "<li class='submenulink' name='".$row['ind']."'>".$row['title']."</li>";
				$i++;
			}
			$result->close();
		}
	}

	function strposa($haystack, $needle, $offset=0) {
		if(!is_array($needle)) $needle = array($needle);
		foreach($needle as $query) {
			if(strpos($haystack, $query, $offset) == true) return true; // stop on first true result
		}
		return false;
	}

	function substr_unicode($str, $s, $l = null) {
		return join("", array_slice(
			preg_split("//u", $str, -1, PREG_SPLIT_NO_EMPTY), $s, $l));
	}
	
	function cleanString($str) {
		return preg_replace("/\r|\n/","",$str);
	}
	
	/**
	 * Get a web file (HTML, XHTML, XML, image, etc.) from a URL.  Return an
	 * array containing the HTTP server response header fields and content.
	 */
	function get_web_page( $url )
	{
		$options = array(
			CURLOPT_RETURNTRANSFER => true,     // return web page
			CURLOPT_HEADER         => false,    // don't return headers
			// CURLOPT_FOLLOWLOCATION => true,     // follow redirects
			CURLOPT_ENCODING       => "",       // handle all encodings
			CURLOPT_USERAGENT      => "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.13) Gecko/20080311 Firefox/2.0.0.13", // who am i
			CURLOPT_AUTOREFERER    => true,     // set referer on redirect
			CURLOPT_CONNECTTIMEOUT => 120,      // timeout on connect
			CURLOPT_TIMEOUT        => 120,      // timeout on response
			CURLOPT_MAXREDIRS      => 10,       // stop after 10 redirects
			CURLOPT_COOKIEJAR      => "",
			CURLOPT_COOKIEFILE     => "",
		);

		$ch      = curl_init( $url );
		curl_setopt_array( $ch, $options );
		$content = curl_exec( $ch );
		$err     = curl_errno( $ch );
		$errmsg  = curl_error( $ch );
		$header  = curl_getinfo( $ch );
		curl_close( $ch );

		$header['errno']   = $err;
		$header['errmsg']  = $errmsg;
		$header['content'] = $content;
		return $header;
		
	}

?>
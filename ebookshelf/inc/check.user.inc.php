<?php
require_once 'settings.inc.php';
require_once 'functions.inc.php';

$actual_link = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

openConnection();

$SQL = "SELECT teacher_name, teacher_login FROM ebook_teacher WHERE teacher_id='".$_SESSION['user_session']."'";
if ($result = $objConn->query($SQL)) {
	$row_cnt = $result->num_rows;
	if ( $row_cnt == 1 ) {
		$row = $result->fetch_assoc();
		$user_name = $row['teacher_name'];
		$user_login = isset($row['teacher_login']) ? $row['teacher_login'] : '';
		
		$SQLOG = "INSERT INTO ebook_log (log,path,teacher_id,teacher_login,edit_date,book_id,page,class) VALUES ";
		$SQLOG .= "('user check successfully.','".$actual_link."','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."',0,0,'')";
		// $objConn->query($SQLOG);

	} else {
		$user_login = ''; // 定义变量以避免未定义错误
		$SQLOG = "INSERT INTO ebook_log (log,path,teacher_id,teacher_login,edit_date,book_id,page,class) VALUES ";
		$SQLOG .= "('user check error: 1','".$actual_link."','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."',0,0,'')";
		$objConn->query($SQLOG);

		session_unset();
		if(session_destroy()) {
			header("Location: /ebookshelf/login.php");
		}
	}
	$result->close();
} else {
	$user_login = ''; // 定义变量以避免未定义错误
	$SQLOG = "INSERT INTO ebook_log (log,path,teacher_id,teacher_login,edit_date,book_id,page,class) VALUES ";
	$SQLOG .= "('user check error: 2','".$actual_link."','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."',0,0,'')";
	$objConn->query($SQLOG);

	session_unset();
	if(session_destroy()) {
		header("Location: /ebookshelf//login.php");
	}
}
closeConnection();


?>

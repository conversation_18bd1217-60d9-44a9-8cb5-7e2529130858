﻿
<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<title>卓思 EbookShelf</title>
	<style>
html, body {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	user-select: none;
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}
header {
	width: 100%;
	height: 60px;
	padding: 0;
	margin: 0;
	background-image: url(img/header.jpg);
	background-repeat: repeat-x;
	background-position: center; 
}
#header-menu {
	width:100%;
	height:100%;
	margin: 0 auto;
	border-collapse: collapse;
}
#header-menu td{
	vertical-align:middle;
	text-align:center;
	height:100%;
}
main {
	width: 100%;
	height: calc(100% - 80px); 
	padding: 0;
	margin: 0;
	overflow: auto;
}
.book-cover {
	width:180px;
	height:180px;
	padding:10px;
	display:inline-block;
	text-align:center;
	vertical-align:middle;
}
.book-cover:hover {
	background-color:#FFE4E1;
}
.book-cover img{
	width:100%;
	height:100%;
	object-fit:contain;
}
.book-disabled {
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	-ms-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}

.login {
	width: 400px;
	margin: 100px auto;
	font-size: 20px;
	padding: 0;
	box-shadow: 0px 0px 20px #888888;
	padding-bottom: 20px;
	overflow: hidden;
	border-radius: 10px;
}
#error {
	color: red;
}
.login h3 {
	margin:0;
	margin-bottom:10px;
	padding-top:10px;
	padding-bottom:10px;
	padding-left:50px;
	padding-right:50px;
	background-color:PaleGreen;
	color:DarkOliveGreen;
}
#login, #password {
	width: 290px;
	height: 20px;
	padding: 5px;
	font-size: 20px;
	text-align: center;
	margin-bottom: 10px;
}
#btn-login{
	width: 300px;
	height: 40px;	
	margin-top: 10px;
	font-size: 20px;
}
	</style>
	<script src="https://code.jquery.com/jquery-1.7.2.min.js"></script>
	<script>
$(document).ready(function() {

    console.log("ready!");
	function isLocalStorageNameSupported() {
	  var testKey = 'test', storage = window.localStorage;
	  try {
		storage.setItem(testKey, '1');
		storage.removeItem(testKey);
		return true;
	  } catch (error) {
		return false;
	  }
	}

	if ( isLocalStorageNameSupported() ) {
		if (localStorage.getItem('jimp_ebook_login') == null) {
			localStorage.setItem('jimp_ebook_login', '');
			$("#login").val('');
			$('#save-pwd').prop('checked', false);
		} else {
			if (localStorage.getItem("jimp_ebook_login") == '') {
				$("#login").val(localStorage.getItem("jimp_ebook_login"));
				$('#save-pwd').prop('checked', false);
			} else {
				$("#login").val(localStorage.getItem("jimp_ebook_login"));
				$('#save-pwd').prop('checked', true);
			}
		}
		if (localStorage.getItem('jimp_ebook_pwd') == null) {
			localStorage.setItem('jimp_ebook_pwd', '');
			$("#password").val('');
		} else {
			$("#password").val(localStorage.getItem("jimp_ebook_pwd"));
		}
	}

    /* login submit */
    $("#btn-login").bind("click", function(event) {

        event.preventDefault();

        if ($.trim($('#login').val()) == '') {
            $("#error").text('請輸入登入名稱。').show();
            $('#login').focus();
            return false;
        }

        if ($.trim($('#password').val()) == '') {
            $("#error").text('請輸入密碼。').show();
            $('#password').focus();
            return false;
        }
		
		if ( isLocalStorageNameSupported() ) {
			if ($('#save-pwd').is(":checked")) {
				localStorage.setItem('jimp_ebook_login', $("#login").val());
				localStorage.setItem('jimp_ebook_pwd', $("#password").val());
			} else {
				localStorage.setItem('jimp_ebook_login', '');
				localStorage.setItem('jimp_ebook_pwd', '');
			}
		}

        $.ajax({

            type: 'POST',
            url: 'inc/login_process.php',
            data: {
                'login': $('#login').val(),
                'password': $('#password').val()
            },
            beforeSend: function() {
                $("#error").fadeOut();
                $("#btn-login").html(' 驗證中 ...');
            },
            success: function(response) {
                console.log(response);
                if (response == "ok") {
                    $("#btn-login").html('登入中 ...');
                    setTimeout(' window.location.href = "index.php";', 500);
				} else if ( response == "good" ) {
                    $("#btn-login").html('登入中 ...');
                    setTimeout(' window.location.href = "admin/";', 500);
                } else {
                    $("#error").fadeIn(100, function() {
                        $("#error").text(response);
                        $("#btn-login").html('登入');
                    });
                }
            }
        });

    });


});	
	</script>
</head>
<body>
	<header>
		<table id="header-menu">
			<tr>
				<td width="220"><a href="https://www.excellence.com.hk/" target="_blank" ><img src="img/logo.png" height="43" width="199"></td>
				<td> </td>
				<td width="200"> </td>
			</tr>
		</table>
	</header>
	<main>
	
		<div class="login" >
			<h3 style="">請登入</h3>
			<div id="login-form" style="margin:0 50px;">
			
				<div id="error">
				<!-- error will be shown here ! -->
				</div>
				
				<input type="text" name="login" id="login" value="" placeholder="登入名稱" ><br>
				
				<input type="password" name="password" id="password" value="" placeholder="密碼" ><br>
				
				<input type="checkbox" name="save-pwd" id="save-pwd" >
				<label for="save-pwd">記住我</label><br>
				
				<button id="btn-login">登入</button>
			
			</div>
		</div>

	</main>
	

</body>
</html>
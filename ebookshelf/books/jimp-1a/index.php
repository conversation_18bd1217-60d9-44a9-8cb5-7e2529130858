﻿<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] == 1 ) {
		require_once '../../inc/check.user.inc.php';
	} else {
		require_once '../../inc/check.student.inc.php';
	}
}

?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, user-scalable=no" />
	<!-- <meta name="viewport" content="width = 1100, user-scalable = no" /> -->
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="icon" href="favicon.ico" type="image/x-icon">

	<title>小學音樂之旅 1 上</title>

	<link rel="stylesheet" href="/min/?g=ebookshelf-css">
	
	<script type="text/javascript" src="/min/?g=ebookshelf-js"></script>
	<script type="text/javascript" src="/min/?g=ebookshelf-js2" async></script>
	<script type="text/javascript" src="/min/?g=ebookshelf-js3"></script>
	
	</head>
<body>
 
<div id="canvas">

<div class="home-icon" title="返回書櫃" ></div>

<div class="zoom-icon zoom-icon-in" title="放大"></div>

<div class="zoom-icon zoom-icon-out" title="縮小"></div>

<div class="page-icon page-icon-double" title="單雙頁"></div>

<div class="content-icon" title="目錄"></div>

<div class="note-icon" title="筆記"></div>

<div class="highlighter-icon" title="螢光筆"></div>

<div class="bookmark-icon" title="書簽"></div>

<div class="test-icon" title="測驗結果"></div>

<div class="magazine-viewport">
	<div class="container">
		<div class="magazine">
			<!-- Next button -->
			<div ignore="1" class="next-button"></div>
			<!-- Previous button -->
			<div ignore="1" class="previous-button"></div>
		</div>
	</div>
	<div class="bottom">
		<div id="slider-bar" class="turnjs-slider">
			<div id="slider"></div>
		</div>
	</div>
</div>

<div id="dialogAudio" class="eBookDialog" title="Audio dialog" style="overflow:hidden;" >
	<audio id="myAudio" class="js-player" controls style="width:100%;height:100%;">
	  <source id="mp3_src" src="" type="audio/mpeg">
		Your browser does not support the audio element.
	</audio>
</div>

<div id="dialogVideo" class="eBookDialog" title="Video dialog" style="background-color:black;overflow:hidden;">
	<video id="myVideo" width="100%" height="100%" controls style="background-color:black;">
		<source id="mp4_src" src="" type="video/mp4">
		Your browser does not support the video tag.
	</video>
</div>

<div id="dialogHtml5" class="eBookDialog" title="HTML5 dialog" style="padding:0;overflow:hidden;">
	<iframe id="myHtml5" frameBorder="0" scrolling="no" src="" height="100%" width="100%" ></iframe>
</div>

<div id="dialogNotes" class="eBookDialog" title="Notes dialog" style="overflow:hidden;" >
	<p style="text-align:justify;" ></p>
</div>

<div id="dialogCustomNote" class="eBookDialog" title="Notes dialog" style="overflow:hidden;" >
	<p style="text-align:justify;" ></p>
	<textarea style="width:300px;height:300px;display:hidden;font-size:12px;resize: none;"></textarea>
	<input type="hidden" id="custom-id" >
</div>

<div id="dialogBookmark" class="eBookDialog" title="書簽" style="overflow:hidden;" >
	<div id="bookmark-content" style="height:219px;width:164px;overflow-x:hidden;">
	</div>
	<div style="height:30px;width:164px;overflow:hidden;">
		<button id="bookmark-add" class="bookmark-btn">加入本頁</button> <button  id="bookmark-del" class="bookmark-btn">刪除本頁</button>
	</div>
</div>
         
<div id="dialogTest" class="eBookDialog" title="測驗成績" style="overflow: hidden">
	<div id="testWarning" style="color:red;" ></div>
	<div id="testTable" style="overflow-x: hidden; overflow-y: auto;" >
		<table id="testTable2">
			<tr><th>頁數</th><th>測驗</th><th>名稱</th><th>成績</th></tr>
			<tr data-page="03" data-number="1"><td>P.03</td><td>1</td><td><a class="testHtml5">我發現</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="04" data-number="1"><td>P.04</td><td>1</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="05" data-number="1"><td>P.05</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="07" data-number="1"><td>P.07</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="10" data-number="1"><td>P.10</td><td>1</td><td><a class="testHtml5">演藝互動區</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="10" data-number="2"><td>P.10</td><td>2</td><td><a class="testHtml5">演藝互動區</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="13" data-number="1"><td>P.13</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="13" data-number="2"><td>P.13</td><td>2</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="18" data-number="1"><td>P.18</td><td>1</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="18" data-number="2"><td>P.18</td><td>2</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="20" data-number="1"><td>P.20</td><td>1</td><td><a class="testHtml5">創意廊</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="23" data-number="1"><td>P.23</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="27" data-number="1"><td>P.27</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="31" data-number="1"><td>P.31</td><td>1</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="32" data-number="1"><td>P.32</td><td>1</td><td><a class="testHtml5">我發現</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="33" data-number="1"><td>P.33</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="38" data-number="1"><td>P.38</td><td>1</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="40" data-number="1"><td>P.40</td><td>1</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="46" data-number="1"><td>P.46</td><td>1</td><td><a class="testHtml5">演藝互動區</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="46" data-number="2"><td>P.46</td><td>2</td><td><a class="testHtml5">聽力實驗室</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="46" data-number="3"><td>P.46</td><td>3</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="47" data-number="1"><td>P.47</td><td>1</td><td><a class="testHtml5">活動地帶</a></td><td style="color:blue;">未完成</td></tr>
			<tr data-page="49" data-number="1"><td>P.49</td><td>1</td><td><a class="testHtml5">演藝互動區</a></td><td style="color:blue;">未完成</td></tr>
		</table>
		<br><br>
	</div>
</div>


<div id="lc" style="display:none;">
	<div id="lc-canvas" class="literally core" style="position:absolute;"></div>
	<div id="pen1" class="lc-pen" style="top:10px;"></div>
	<div id="pen2" class="lc-pen" style="top:70px;"></div>
	<div id="pen3" class="lc-pen" style="top:130px;"></div>
	<div id="eraser" class="lc-pen" style="top:190px;"></div>

	<div id="lc-zoom-in" class="lc-btn" style="top:250px;"></div>
	<div id="lc-zoom-out" class="lc-btn" style="top:310px;"></div>

	<div id="lc-clear" class="lc-btn" style="bottom:10px;"></div>
	<div id="lc-redo" class="lc-btn" style="bottom:70px;"></div>
	<div id="lc-undo" class="lc-btn" style="bottom:130px;"></div>
</div>

</div>

<script type="text/javascript">


$('#canvas').hide();

var pageName = {
	'1':'封面',
	'2':'留空',
	'3':'i',
	'4':'ii',
	'5':'iii',
	'6':'iv',
	'7':'v',
	'8':'1',
	'9':'2',
	'10':'3',
	'11':'4',
	'12':'5',
	'13':'6',
	'14':'7',
	'15':'8',
	'16':'9',
	'17':'10',
	'18':'11',
	'19':'12',
	'20':'13',
	'21':'14',
	'22':'15',
	'23':'16',
	'24':'17',
	'25':'18',
	'26':'19',
	'27':'20',
	'28':'21',
	'29':'22',
	'30':'23',
	'31':'24',
	'32':'25',
	'33':'26',
	'34':'27',
	'35':'28',
	'36':'29',
	'37':'30',
	'38':'31',
	'39':'32',
	'40':'33',
	'41':'34',
	'42':'35',
	'43':'36',
	'44':'37',
	'45':'38',
	'46':'39',
	'47':'40',
	'48':'41',
	'49':'42',
	'50':'43',
	'51':'44',
	'52':'45',
	'53':'46',
	'54':'47',
	'55':'48',
	'56':'49',
	'57':'50',
	'58':'51',
	'59':'留空',
	'60':'封底'
};

var players = plyr.setup('.js-player',{volume:5});
var contentPage = 7;
var bookID = 1;
var bookLang = 'chi';
var bookPath = 'jimp-1a';
var disableZoom = false;
var customNoteAdd = false;
// var flipBookHeight = 975;
// var flipBookWidth = 1508;
var flipBookHeight = 1000;
var flipBookWidth = 1562;
var flipBookSingleWidth = parseInt( flipBookWidth / 2 );
var flipBookPages = 60;
var flipBookDisplay = "double";
var zoomMax = 1.5;
var usertype = <?php echo $_SESSION['type']; ?>;

// Load the HTML4 version if there's not CSS transform
yepnope({
	test : Modernizr.csstransforms,
	yep: ['/min/?g=ebookshelf-js4'],
	nope: ['/min/?g=ebookshelf-js5','/min/?g=ebookshelf-2.css'],
	both: ['/min/?g=ebookshelf-js6','/min/?g=ebookshelf-3.css'],
	complete: loadApp
});

</script>

</body>
</html>
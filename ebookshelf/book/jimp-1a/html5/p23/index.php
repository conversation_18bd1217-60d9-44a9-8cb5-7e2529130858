<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=800, user-scalable=no">	
	<title>聽力實驗室</title>
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-mc-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 23;
		var number = 1;
		var pageTotal = 1;
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-mc-js"></script>
	<style>
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">

	<div id="page1" class="page" >
		<table class="main" >
			<tr style="height:110px;" >
				<td width="100%" >
					<div style="margin-top:5px;margin-bottom:5px;" >
						聆聽老師分別播放以下歌曲的純音樂版本，並選出包含四分休止符的一首。
					</div>
				</td>
			</tr>
			<tr style="height:320px;" >
				<td width="340" >
					<table width='100%'>
						<tr><td width='100%' style='vertical-align:middle;'><span class='draggable' value='A'>A</span> 佚名《Pitter Patter》<img src="img/track.png" width="50" alt="" data-mp3="1a33.mp3" class="track-btn disable"> </td></tr>
						<tr><td width='100%' style='vertical-align:middle;'><span class='draggable' value='B'>B</span> 德國民歌《Summ, Summ, Summ》<img src="img/track.png" width="50" alt="" data-mp3="1a34.mp3" class="track-btn disable"> </td></tr>
					</table>
					<div class='mc_answer' value='B'></div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="warning"></div>
					<button class="myButton" id="ans-btn">提交</button>
				</td>
			</tr>
		</table>
	</div>
	
</div>

</body>
</html>

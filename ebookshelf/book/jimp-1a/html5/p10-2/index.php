<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=850, user-scalable=no">	
	<title>演藝互動區</title>
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-drag-css">
	<style>
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
span.ans {
	display: inline-block;
	width: 50px;
	border-bottom: 1px solid black;
}
img.dragImg {
	width:65px;
}
	</style>
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 10;
		var number = 2;
		var matchAns = [
			{ans:"出", img:""},
			{ans:"力", img:""},
		];
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-drag-js"></script>
</head>
<body>

<div id="main">
	<div style="margin-top:5px;margin-bottom:5px;" >
		試把正確答案拉到橫線上。
	</div>
	
	<table style="width:60%;height:80px;margin-top:10px;" >
		<tr>
			<td class='match-droppable'><span class='match-draggable' data-ans=''></span></td>
			<td class='match-droppable'><span class='match-draggable' data-ans=''></span></td>
		</tr>
	</table>
	
	<table class="main" >
		<tr>
			<td style="text-align:left;">
			
				<img src="img/track.png" width="50" alt="" data-mp3="1a19.mp3" class="track-btn disable"><br>
			
				在《同心協力》一曲中，歌詞中的「一」、「三」和「<span class='match-droppable ans' data-drop="" data-ans='出'>&nbsp;</span>」較高音，「二」和「<span class='match-droppable ans' data-drop="" data-ans='力'>&nbsp;</span>」字則較低音。
				
			</td>
		</tr>
	</table>
	
	<div class="warning"></div>
	<button class="myButton" id="ans-btn" style="margin-top:10px;">提交</button>
	
</div>

</body>
</html>

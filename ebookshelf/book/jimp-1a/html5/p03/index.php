<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>我發現</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-radio-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 3;
		var number = 1;
		var qArray = [
			{name:"q-1", ans:"1"},
			{name:"q-2", ans:"2"},
		];
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-radio-js"></script>
	<style>
div#main {
	font-family: DFKai-sb;
	font-size: 28px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%" cellspacing="0">
	<tr>
		<td style="line-height:45px;">
			試選擇正確答案。
			<br><br>
			「歡迎你」三個字由不同高低的音組成，當中以「歡」字較
			<span style="background-color:orange;">
				1.<input type="radio" name="q-1" id="q-1-1" value="1"><label class="choice" for="q-1-1">高</label> 
				<input type="radio" name="q-1" id="q-1-2" value="2"><label class="choice" for="q-1-2">低</label> 
			</span>
			音，「迎」字較
			<span style="background-color:orange;">
				2.<input type="radio" name="q-2" id="q-2-1" value="1"><label class="choice" for="q-2-1">高</label> 
				<input type="radio" name="q-2" id="q-2-2" value="2"><label class="choice" for="q-2-2">低</label> 
			</span>
			音。
		</td>
	</tr>
	<tr>
		<td>
			<br>
			<div class="warning"></div>
			<button class="myButton" id="ans-btn">提交</button>
		</td>
	</tr>
	</table>
</div>


</body>
</html>
<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>我發現</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-radio-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 32;
		var number = 1;
		var qArray = [
			{name:"q-1", ans:"4"},
		];
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-radio-js"></script>
	<style>
* {
	font-family: DFKai-sb;
	font-size: 28px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%" cellspacing="0">
	<tr>
		<td style="line-height:45px;">
			試選擇正確答案。
			<br><br>
			《開心歌唱》共有
			<span style="background-color:orange;">
				1.<input type="radio" name="q-1" id="q-1-1" value="3"><label class="choice" for="q-1-1">三</label> 
				<input type="radio" name="q-1" id="q-1-2" value="4"><label class="choice" for="q-1-2">四</label> 
				<input type="radio" name="q-1" id="q-1-3" value="5"><label class="choice" for="q-1-3">五</label> 
			</span>
			句樂句，歌譜中以( <img src="img/001.jpg" height="30" alt="" > ) 來表示。
		</td>
	</tr>
	<tr>
		<td>
			<br>
			<div class="warning"></div>
			<button class="myButton" id="ans-btn">提交</button>
		</td>
	</tr>
	</table>
</div>


</body>
</html>
<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>活動地帶</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-radio-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 18;
		var number = 1;
		var qArray = [
			{name:"q-1", ans:"3"},
		];
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-radio-js"></script>
	<style>
* {
	font-family: DFKai-sb;
	font-size: 28px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%" cellspacing="0">
	<tr>
		<td style="line-height:45px;">
			試選擇正確答案。
			<br><br>
			音符A.<img src="img/001.jpg" height="50" alt="" > 音符B.<img src="img/002.jpg" height="50" alt="" > 音符C.<img src="img/003.jpg" height="50" alt="" >  
			<br>
			2. 我發現音符 
			<span style="background-color:orange;">
				1.<input type="radio" name="q-1" id="q-1-1" value="1"><label class="choice" for="q-1-1">A</label> 
				<input type="radio" name="q-1" id="q-1-2" value="2"><label class="choice" for="q-1-2">B</label> 
				<input type="radio" name="q-1" id="q-1-3" value="3"><label class="choice" for="q-1-3">C</label> 
			</span>
			的時值最短。
		</td>
	</tr>
	<tr>
		<td>
			<br>
			<div class="warning"></div>
			<button class="myButton" id="ans-btn">提交</button>
		</td>
	</tr>
	</table>
</div>


</body>
</html>
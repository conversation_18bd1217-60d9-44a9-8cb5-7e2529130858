<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=850, user-scalable=no">	
	<title>演藝互動區</title>
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-drag-css">
	<style>
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
span.ans {
	display: inline-block;
	vertical-align:bottom;
	width:90px;
	height:90px;
	border-bottom:1px solid black;
}
img.dragImg {
	width:70px;
}
	</style>
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 40;
		var number = 1;
		var matchAns = [
			{ans:"001", img:"001.jpg"},
			{ans:"002", img:"002.jpg"},
			{ans:"003", img:"003.jpg"},
			{ans:"004", img:"004.jpg"},
		];	
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-drag-js"></script>
</head>
<body>

	<div style="margin-top:5px;margin-bottom:5px;" >
		聆聽老師分別播放以下樂器的演奏選段，分辨它們的音色，把適當的樂器圖片拉到橫線上。<br>
		(1) <img src="img/track.png" width="50" alt="" data-mp3="1a57.mp3" class="track-btn disable">
		(2) <img src="img/track.png" width="50" alt="" data-mp3="1a58.mp3" class="track-btn disable">
		(3) <img src="img/track.png" width="50" alt="" data-mp3="1a59.mp3" class="track-btn disable">
		(4) <img src="img/track.png" width="50" alt="" data-mp3="1a60.mp3" class="track-btn disable">
	</div>
	<br>
	<table style="width:100%;margin-top:10px;" >
		<tr>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
		</tr>
	</table>
	<br>
	<table class="main" style="width:100%;margin-top:10px;" >
		<tr>
			<td>
				(1) <span class='match-droppable ans' data-drop="" data-ans='004'>&nbsp;</span>
			</td>
			<td>
				(2) <span class='match-droppable ans' data-drop="" data-ans='003'>&nbsp;</span>
			</td>
			<td>
				(3) <span class='match-droppable ans' data-drop="" data-ans='002'>&nbsp;</span>
			</td>
			<td>
				(4) <span class='match-droppable ans' data-drop="" data-ans='001'>&nbsp;</span>
			</td>
		</tr>
	</table>
	
	<div class="warning"></div>
	<button class="myButton" id="ans-btn" style="margin-top:10px;">提交</button>
	
</div>

</body>
</html>

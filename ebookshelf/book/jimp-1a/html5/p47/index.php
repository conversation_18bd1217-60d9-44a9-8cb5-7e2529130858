<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=850, user-scalable=no">	
	<title>活動地帶</title>
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-drag-css">
	<style>
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
span.ans {
	display: inline-block;
	vertical-align:bottom;
	width:100px;
	height:110px;
	border-bottom:1px solid black;
}
img.dragImg {
	width:100px;
}
	</style>
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 47;
		var number = 1;
		var matchAns = [
			{ans:"001", img:"001.jpg"},
			{ans:"002", img:"002.jpg"},
		];	
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-drag-js"></script>
	</script>
</head>
<body>

	<div style="margin-top:5px;margin-bottom:5px;" >
		1. 聆聽以下樂曲選段，哪段能表現毛毛的動作速度？哪段能表現寶寶的呢？試把毛毛和寶寶分別拖拉到適當的方格內。
	</div>
	<br>
	<table style="width:100%;margin-top:10px;" >
		<tr>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
			<td class='match-droppable'><span class='match-draggable' > </span></td>
		</tr>
	</table>
	<br>
	<table class="main" border="1" style="width:100%;margin-top:10px;" >
		<tr>
			<td width="60%">歌曲選段</td><td>毛毛 / 寶寶</td>
		</tr>
		<tr>
			<td>(1) 舒曼：《兒時情景：夢幻曲》(選段)<img src="img/track.png" width="50" alt="" data-mp3="1a69.mp3" class="track-btn disable"></td>
			<td><span class='match-droppable ans' data-drop="" data-ans='002'>&nbsp;</span></td>
		</tr>
		<tr>
			<td>(2) 蕭邦《降D大調圓舞曲：一分鐘圓舞曲》(選段)<img src="img/track.png" width="50" alt="" data-mp3="1a70.mp3" class="track-btn disable"></td>
			<td><span class='match-droppable ans' data-drop="" data-ans='001'>&nbsp;</span></td>
		</tr>
	</table>
	
	<div class="warning"></div>
	<button class="myButton" id="ans-btn" style="margin-top:10px;">提交</button>
	
</div>

</body>
</html>

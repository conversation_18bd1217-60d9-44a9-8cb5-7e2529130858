<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>聽力實驗室</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-text-css">
	<style>
* {
	font-family: DFKai-sb;
	font-size: 24px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 5;
		var number = 1;	
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-text-js"></script>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%">
	<tr>
		<td>
			聆聽以下旋律，並排列它們出現的次序。<br>
			<img src="img/track.png" width="50" alt="" data-mp3="1a10.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a11.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a12.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a13.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a14.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a15.mp3" class="track-btn disable"><br>
			<img src="img/q-1.jpg" width="450" alt="">
			
		</td>
	</tr>
	<tr>
		<td>
			出現次序： 1. <input type="text" class="ans" id="q-1-1" data-ans="C,c" size="5" >
			&rarr;  2. <input type="text" class="ans" id="q-1-2" data-ans="A,a" size="5" >
			&rarr;  3. <input type="text" class="ans" id="q-1-3" data-ans="E,e" size="5" > <br>
			&rarr;  4. <input type="text" class="ans" id="q-1-4" data-ans="D,d" size="5" >
			&rarr;  5. <input type="text" class="ans" id="q-1-5" data-ans="B,b" size="5" >
			&rarr;  6. <input type="text" class="ans" id="q-1-6" data-ans="F,f" size="5" >

		</td>
	</tr>
	<tr><td><br><button class="myButton" id="ans-btn">提交</button></td></tr>
	<tr><td class="warning"></td></tr>
	</table>
	
</div>

</body>
</html>
<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>聽力實驗室</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-radio-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 13;
		var number = 1;
		var qArray = [
			{name:"q-1", ans:"8"},
		];
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-radio-js"></script>
	<style>
* {
	font-family: DFKai-sb;
	font-size: 28px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%" cellspacing="0">
	<tr>
		<td style="line-height:45px;">
			1. 聆聽聖桑《動物狂歡節：鋼琴家》(選段)，選擇正確答案。
			<img src="img/track.png" width="50" alt="" data-mp3="1a23.mp3" class="track-btn disable">
			<br><br>
			在這樂曲選段中，“ d  r  m  f  s  f  m  r” 這一組唱名共出現了
			<span style="background-color:orange;">
				1.<input type="radio" name="q-1" id="q-1-1" value="7"><label class="choice" for="q-1-1">七</label> 
				<input type="radio" name="q-1" id="q-1-2" value="8"><label class="choice" for="q-1-2">八</label> 
				<input type="radio" name="q-1" id="q-1-3" value="9"><label class="choice" for="q-1-3">九</label> 
			</span>
			次。
			<br>( <img src="img/001.jpg" height="75" alt="" > 中已包括了兩次。)
		</td>
	</tr>
	<tr>
		<td colspan="4">
			<br>
			<div class="warning"></div>
			<button class="myButton" id="ans-btn">提交</button>
		</td>
	</tr>
	</table>
</div>


</body>
</html>
<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>聽力實驗室</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-text-css">
	<script type="text/javascript">
		var bookLang ="chi";
		var book_id = 1;
		var page = 27;
		var number = 1;
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-text-js"></script>
	<style>
* {
	font-family: DFKai-sb;
	font-size: 24px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
	</style>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%">
	<tr>
		<td>
			聆聽德國民歌《Merry May》(選段) 的三個版本，並排列它們出現的次序。<br>
			<img src="img/track.png" width="50" alt="" data-mp3="1a38.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a37.mp3" class="track-btn disable"> &rarr; <img src="img/track.png" width="50" alt="" data-mp3="1a39.mp3" class="track-btn disable"> <br>
			<img src="img/q-1.jpg" width="450" alt="">
			
		</td>
	</tr>
	<tr>
		<td>
			出現次序： 1. <input type="text" class="ans" data-ans="B,b" id="q-1-1" size="5" >
			&rarr;  2. <input type="text" class="ans" data-ans="A,a" id="q-1-2" size="5" >
			&rarr;  3. <input type="text" class="ans" data-ans="C,c" id="q-1-3" size="5" >

		</td>
	</tr>
	<tr><td><br><button class="myButton" id="ans-btn">提交</button></td></tr>
	<tr><td class="warning"></td></tr>
	</table>
	
</div>

</body>
</html>
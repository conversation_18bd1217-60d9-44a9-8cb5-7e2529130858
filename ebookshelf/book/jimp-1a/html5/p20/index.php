<?php
session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: ../../../../please_login.php");
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>創意廊</title>
	<meta name="viewport" content="width=850, user-scalable=no">
	<link rel="stylesheet" href="/min/?g=ebookshelf-html5-open-css">
	<style>
* {
	font-family: DFKai-sb;
	font-size: 24px;
}
.warning {
	font-family: PMingLiU;
	font-size: 16px;
	color: red;
}
.ans {
	text-align: left;
}
	</style>
	<script type="text/javascript">
		// 開放題型
		var bookLang ="chi";
		var book_id = 1;
		var page = 20;
		var number = 1;
	</script>
	<script type="text/javascript" src="/min/?g=ebookshelf-html5-open-js"></script>
</head>
<body>

<div id="main">
	<table width="90%" style="margin-left:5%">
	<tr>
		<td>
			請分組為以下節奏創作句子，再按節奏讀出來吧！
			<img src="img/track.png" width="50" alt="" data-mp3="1a_p20_01.mp3" class="track-btn disable"><br>
			<img src="img/001.jpg" width="500" alt="" >
		</td>
	</tr>
	<tr>
		<td>
			<br>句子：(不多於30字)<br>
			<input type="text" class="ans" id="q-1-1" size="60" maxlength="35">
		</td>
	</tr>
	<tr><td><br><button class="myButton" id="ans-btn">提交</button></td></tr>
	<tr><td class="warning"></td></tr>
	</table>
	
</div>

</body>
</html>
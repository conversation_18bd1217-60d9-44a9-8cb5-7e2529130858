





<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">



  <link crossorigin="anonymous" href="https://assets-cdn.github.com/assets/frameworks-eb63b5ca175f20151e478297e2205d5d6ac5acc8068a83c1d838e50e91689df4.css" integrity="sha256-62O1yhdfIBUeR4KX4iBdXWrFrMgGioPB2DjlDpFonfQ=" media="all" rel="stylesheet" />
  <link crossorigin="anonymous" href="https://assets-cdn.github.com/assets/github-ce0c4ea874809df80d4e80fd270d0e13ea4f011c06ef9875b89fe1725aedff36.css" integrity="sha256-zgxOqHSAnfgNToD9Jw0OE+pPARwG75h1uJ/hclrt/zY=" media="all" rel="stylesheet" />
  
  
  <link crossorigin="anonymous" href="https://assets-cdn.github.com/assets/site-f85b78c12cd707dafadc4d9e5e825e43bc3b3cc1283a814952649102189d669f.css" integrity="sha256-+Ft4wSzXB9r63E2eXoJeQ7w7PMEoOoFJUmSRAhidZp8=" media="all" rel="stylesheet" />
  

  <meta name="viewport" content="width=device-width">
  
  <title>Synchronize.js/synchronize-min.js at master · CallToPower/Synchronize.js · GitHub</title>
  <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="GitHub">
  <link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub">
  <meta property="fb:app_id" content="1401488693436528">


  <link rel="assets" href="https://assets-cdn.github.com/">
  
  <meta name="pjax-timeout" content="1000">
  
  <meta name="request-id" content="B999:1E9AF:5C3849:95DC43:58B7D49F" data-pjax-transient>
  

  <meta name="selected-link" value="repo_source" data-pjax-transient>

  <meta name="google-site-verification" content="KT5gs8h0wvaagLKAVWq8bbeNwnZZK1r1XQysX3xurLU">
<meta name="google-site-verification" content="ZzhVyEFwb7w3e0-uOTltm8Jsck2F5StVihD0exw2fsA">
    <meta name="google-analytics" content="UA-3769691-2">

<meta content="collector.githubapp.com" name="octolytics-host" /><meta content="github" name="octolytics-app-id" /><meta content="https://collector.githubapp.com/github-external/browser_event" name="octolytics-event-url" /><meta content="B999:1E9AF:5C3849:95DC43:58B7D49F" name="octolytics-dimension-request_id" />
<meta content="/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show" data-pjax-transient="true" name="analytics-location" />



  <meta class="js-ga-set" name="dimension1" content="Logged Out">



      <meta name="hostname" content="github.com">
  <meta name="user-login" content="">

      <meta name="expected-hostname" content="github.com">
    <meta name="js-proxy-site-detection-payload" content="********************************************************************************************************************************************************************************************************************************************************************">


  <meta name="html-safe-nonce" content="b5a8e1c790e3202f1b1626be569e2476c6d6c871">

  <meta http-equiv="x-pjax-version" content="e43befffed903825e51be774232c9c58">
  

    
  <meta name="description" content="Synchronize.js - A library for synchronizing multiple HTML5 video elements.">
  <meta name="go-import" content="github.com/CallToPower/Synchronize.js git https://github.com/CallToPower/Synchronize.js.git">

  <meta content="20119" name="octolytics-dimension-user_id" /><meta content="CallToPower" name="octolytics-dimension-user_login" /><meta content="14863529" name="octolytics-dimension-repository_id" /><meta content="CallToPower/Synchronize.js" name="octolytics-dimension-repository_nwo" /><meta content="true" name="octolytics-dimension-repository_public" /><meta content="false" name="octolytics-dimension-repository_is_fork" /><meta content="14863529" name="octolytics-dimension-repository_network_root_id" /><meta content="CallToPower/Synchronize.js" name="octolytics-dimension-repository_network_root_nwo" />
  <link href="https://github.com/CallToPower/Synchronize.js/commits/master.atom" rel="alternate" title="Recent Commits to Synchronize.js:master" type="application/atom+xml">


    <link rel="canonical" href="https://github.com/CallToPower/Synchronize.js/blob/master/src/synchronize-min.js" data-pjax-transient>


  <meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats">

  <meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors">

  <link rel="mask-icon" href="https://assets-cdn.github.com/pinned-octocat.svg" color="#000000">
  <link rel="icon" type="image/x-icon" href="https://assets-cdn.github.com/favicon.ico">

<meta name="theme-color" content="#1e2327">


  <meta name="u2f-support" content="true">

  </head>

  <body class="logged-out env-production windows vis-public page-blob">
    

  <div class="position-relative js-header-wrapper ">
    <a href="#start-of-content" tabindex="1" class="accessibility-aid js-skip-to-content">Skip to content</a>
    <div id="js-pjax-loader-bar" class="pjax-loader-bar"><div class="progress"></div></div>

    
    
    



          <header class="site-header js-details-container Details" role="banner">
  <div class="container-responsive">
    <a class="header-logo-invertocat" href="https://github.com/" aria-label="Homepage" data-ga-click="(Logged out) Header, go to homepage, icon:logo-wordmark">
      <svg aria-hidden="true" class="octicon octicon-mark-github" height="32" version="1.1" viewBox="0 0 16 16" width="32"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>
    </a>

    <button class="btn-link float-right site-header-toggle js-details-target" type="button" aria-label="Toggle navigation">
      <svg aria-hidden="true" class="octicon octicon-three-bars" height="24" version="1.1" viewBox="0 0 12 16" width="18"><path fill-rule="evenodd" d="M11.41 9H.59C0 9 0 8.59 0 8c0-.59 0-1 .59-1H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1h.01zm0-4H.59C0 5 0 4.59 0 4c0-.59 0-1 .59-1H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1h.01zM.59 11H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1H.59C0 13 0 12.59 0 12c0-.59 0-1 .59-1z"/></svg>
    </button>

    <div class="site-header-menu">
      <nav class="site-header-nav">
          <a href="/features" class="js-selected-navigation-item nav-item" data-ga-click="Header, click, Nav menu - item:features" data-selected-links="/features /features">
            Features
</a>          <a href="/explore" class="js-selected-navigation-item nav-item" data-ga-click="Header, click, Nav menu - item:explore" data-selected-links="/explore /trending /trending/developers /integrations /integrations/feature/code /integrations/feature/collaborate /integrations/feature/ship /showcases /explore">
            Explore
</a>        <a href="/pricing" class="js-selected-navigation-item nav-item" data-ga-click="Header, click, Nav menu - item:pricing" data-selected-links="/pricing /pricing">
          Pricing
</a>      </nav>

      <div class="site-header-actions">
          <div class="header-search scoped-search site-scoped-search js-site-search" role="search">
  <!-- '"` --><!-- </textarea></xmp> --></option></form><form accept-charset="UTF-8" action="/CallToPower/Synchronize.js/search" class="js-site-search-form" data-scoped-search-url="/CallToPower/Synchronize.js/search" data-unscoped-search-url="/search" method="get"><div style="margin:0;padding:0;display:inline"><input name="utf8" type="hidden" value="&#x2713;" /></div>
    <label class="form-control header-search-wrapper js-chromeless-input-container">
      <div class="header-search-scope">This repository</div>
      <input type="text"
        class="form-control header-search-input js-site-search-focus js-site-search-field is-clearable"
        data-hotkey="s"
        name="q"
        placeholder="Search"
        aria-label="Search this repository"
        data-unscoped-placeholder="Search GitHub"
        data-scoped-placeholder="Search"
        autocapitalize="off">
    </label>
</form></div>


          <a class="text-bold site-header-link" href="/login?return_to=%2FCallToPower%2FSynchronize.js%2Fblob%2Fmaster%2Fsrc%2Fsynchronize-min.js" data-ga-click="(Logged out) Header, clicked Sign in, text:sign-in">Sign in</a>
            <span class="text-gray">or</span>
            <a class="text-bold site-header-link" href="/join?source=header-repo" data-ga-click="(Logged out) Header, clicked Sign up, text:sign-up">Sign up</a>
      </div>
    </div>
  </div>
</header>


  </div>

  <div id="start-of-content" class="accessibility-aid"></div>

    <div id="js-flash-container">
</div>



  <div role="main">
      <div itemscope itemtype="http://schema.org/SoftwareSourceCode">
    <div id="js-repo-pjax-container" data-pjax-container>
      


<div class="pagehead repohead instapaper_ignore readability-menu experiment-repo-nav">
  <div class="container repohead-details-container">

    

<ul class="pagehead-actions">

  <li>
      <a href="/login?return_to=%2FCallToPower%2FSynchronize.js"
    class="btn btn-sm btn-with-count tooltipped tooltipped-n"
    aria-label="You must be signed in to watch a repository" rel="nofollow">
    <svg aria-hidden="true" class="octicon octicon-eye" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path fill-rule="evenodd" d="M8.06 2C3 2 0 8 0 8s3 6 8.06 6C13 14 16 8 16 8s-3-6-7.94-6zM8 12c-2.2 0-4-1.78-4-4 0-2.2 1.8-4 4-4 2.22 0 4 1.8 4 4 0 2.22-1.78 4-4 4zm2-4c0 1.11-.89 2-2 2-1.11 0-2-.89-2-2 0-1.11.89-2 2-2 1.11 0 2 .89 2 2z"/></svg>
    Watch
  </a>
  <a class="social-count" href="/CallToPower/Synchronize.js/watchers"
     aria-label="3 users are watching this repository">
    3
  </a>

  </li>

  <li>
      <a href="/login?return_to=%2FCallToPower%2FSynchronize.js"
    class="btn btn-sm btn-with-count tooltipped tooltipped-n"
    aria-label="You must be signed in to star a repository" rel="nofollow">
    <svg aria-hidden="true" class="octicon octicon-star" height="16" version="1.1" viewBox="0 0 14 16" width="14"><path fill-rule="evenodd" d="M14 6l-4.9-.64L7 1 4.9 5.36 0 6l3.6 3.26L2.67 14 7 11.67 11.33 14l-.93-4.74z"/></svg>
    Star
  </a>

    <a class="social-count js-social-count" href="/CallToPower/Synchronize.js/stargazers"
      aria-label="22 users starred this repository">
      22
    </a>

  </li>

  <li>
      <a href="/login?return_to=%2FCallToPower%2FSynchronize.js"
        class="btn btn-sm btn-with-count tooltipped tooltipped-n"
        aria-label="You must be signed in to fork a repository" rel="nofollow">
        <svg aria-hidden="true" class="octicon octicon-repo-forked" height="16" version="1.1" viewBox="0 0 10 16" width="10"><path fill-rule="evenodd" d="M8 1a1.993 1.993 0 0 0-1 3.72V6L5 8 3 6V4.72A1.993 1.993 0 0 0 2 1a1.993 1.993 0 0 0-1 3.72V6.5l3 3v1.78A1.993 1.993 0 0 0 5 15a1.993 1.993 0 0 0 1-3.72V9.5l3-3V4.72A1.993 1.993 0 0 0 8 1zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm3 10c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm3-10c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z"/></svg>
        Fork
      </a>

    <a href="/CallToPower/Synchronize.js/network" class="social-count"
       aria-label="9 users forked this repository">
      9
    </a>
  </li>
</ul>

    <h1 class="public ">
  <svg aria-hidden="true" class="octicon octicon-repo" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
  <span class="author" itemprop="author"><a href="/CallToPower" class="url fn" rel="author">CallToPower</a></span><!--
--><span class="path-divider">/</span><!--
--><strong itemprop="name"><a href="/CallToPower/Synchronize.js" data-pjax="#js-repo-pjax-container">Synchronize.js</a></strong>

</h1>

  </div>
  <div class="container">
    
<nav class="reponav js-repo-nav js-sidenav-container-pjax"
     itemscope
     itemtype="http://schema.org/BreadcrumbList"
     role="navigation"
     data-pjax="#js-repo-pjax-container">

  <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
    <a href="/CallToPower/Synchronize.js" class="js-selected-navigation-item selected reponav-item" data-hotkey="g c" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches /CallToPower/Synchronize.js" itemprop="url">
      <svg aria-hidden="true" class="octicon octicon-code" height="16" version="1.1" viewBox="0 0 14 16" width="14"><path fill-rule="evenodd" d="M9.5 3L8 4.5 11.5 8 8 11.5 9.5 13 14 8 9.5 3zm-5 0L0 8l4.5 5L6 11.5 2.5 8 6 4.5 4.5 3z"/></svg>
      <span itemprop="name">Code</span>
      <meta itemprop="position" content="1">
</a>  </span>

    <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
      <a href="/CallToPower/Synchronize.js/issues" class="js-selected-navigation-item reponav-item" data-hotkey="g i" data-selected-links="repo_issues repo_labels repo_milestones /CallToPower/Synchronize.js/issues" itemprop="url">
        <svg aria-hidden="true" class="octicon octicon-issue-opened" height="16" version="1.1" viewBox="0 0 14 16" width="14"><path fill-rule="evenodd" d="M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 0 1 1.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z"/></svg>
        <span itemprop="name">Issues</span>
        <span class="counter">4</span>
        <meta itemprop="position" content="2">
</a>    </span>

  <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
    <a href="/CallToPower/Synchronize.js/pulls" class="js-selected-navigation-item reponav-item" data-hotkey="g p" data-selected-links="repo_pulls /CallToPower/Synchronize.js/pulls" itemprop="url">
      <svg aria-hidden="true" class="octicon octicon-git-pull-request" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M11 11.28V5c-.03-.78-.34-1.47-.94-2.06C9.46 2.35 8.78 2.03 8 2H7V0L4 3l3 3V4h1c.27.02.48.11.69.31.21.2.3.42.31.69v6.28A1.993 1.993 0 0 0 10 15a1.993 1.993 0 0 0 1-3.72zm-1 2.92c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zM4 3c0-1.11-.89-2-2-2a1.993 1.993 0 0 0-1 3.72v6.56A1.993 1.993 0 0 0 2 15a1.993 1.993 0 0 0 1-3.72V4.72c.59-.34 1-.98 1-1.72zm-.8 10c0 .66-.55 1.2-1.2 1.2-.65 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z"/></svg>
      <span itemprop="name">Pull requests</span>
      <span class="counter">2</span>
      <meta itemprop="position" content="3">
</a>  </span>

  <a href="/CallToPower/Synchronize.js/projects" class="js-selected-navigation-item reponav-item" data-selected-links="repo_projects new_repo_project repo_project /CallToPower/Synchronize.js/projects">
    <svg aria-hidden="true" class="octicon octicon-project" height="16" version="1.1" viewBox="0 0 15 16" width="15"><path fill-rule="evenodd" d="M10 12h3V2h-3v10zm-4-2h3V2H6v8zm-4 4h3V2H2v12zm-1 1h13V1H1v14zM14 0H1a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V1a1 1 0 0 0-1-1z"/></svg>
    Projects
    <span class="counter">0</span>
</a>


  <a href="/CallToPower/Synchronize.js/pulse" class="js-selected-navigation-item reponav-item" data-selected-links="pulse /CallToPower/Synchronize.js/pulse">
    <svg aria-hidden="true" class="octicon octicon-pulse" height="16" version="1.1" viewBox="0 0 14 16" width="14"><path fill-rule="evenodd" d="M11.5 8L8.8 5.4 6.6 8.5 5.5 1.6 2.38 8H0v2h3.6l.9-1.8.9 5.4L9 8.5l1.6 1.5H14V8z"/></svg>
    Pulse
</a>
  <a href="/CallToPower/Synchronize.js/graphs" class="js-selected-navigation-item reponav-item" data-selected-links="repo_graphs repo_contributors /CallToPower/Synchronize.js/graphs">
    <svg aria-hidden="true" class="octicon octicon-graph" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path fill-rule="evenodd" d="M16 14v1H0V0h1v14h15zM5 13H3V8h2v5zm4 0H7V3h2v10zm4 0h-2V6h2v7z"/></svg>
    Graphs
</a>

</nav>

  </div>
</div>

<div class="container new-discussion-timeline experiment-repo-nav">
  <div class="repository-content">

    

<a href="/CallToPower/Synchronize.js/blob/b10d4b289c4dff37a26a97ac3c65f34e4b588ab2/src/synchronize-min.js" class="d-none js-permalink-shortcut" data-hotkey="y">Permalink</a>

<!-- blob contrib key: blob_contributors:v21:6ebdb1b33df02ea1d8e54adc6d5188ed -->

<div class="file-navigation js-zeroclipboard-container">
  
<div class="select-menu branch-select-menu js-menu-container js-select-menu float-left">
  <button class="btn btn-sm select-menu-button js-menu-target css-truncate" data-hotkey="w"
    
    type="button" aria-label="Switch branches or tags" tabindex="0" aria-haspopup="true">
    <i>Branch:</i>
    <span class="js-select-button css-truncate-target">master</span>
  </button>

  <div class="select-menu-modal-holder js-menu-content js-navigation-container" data-pjax aria-hidden="true">

    <div class="select-menu-modal">
      <div class="select-menu-header">
        <svg aria-label="Close" class="octicon octicon-x js-menu-close" height="16" role="img" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"/></svg>
        <span class="select-menu-title">Switch branches/tags</span>
      </div>

      <div class="select-menu-filters">
        <div class="select-menu-text-filter">
          <input type="text" aria-label="Filter branches/tags" id="context-commitish-filter-field" class="form-control js-filterable-field js-navigation-enable" placeholder="Filter branches/tags">
        </div>
        <div class="select-menu-tabs">
          <ul>
            <li class="select-menu-tab">
              <a href="#" data-tab-filter="branches" data-filter-placeholder="Filter branches/tags" class="js-select-menu-tab" role="tab">Branches</a>
            </li>
            <li class="select-menu-tab">
              <a href="#" data-tab-filter="tags" data-filter-placeholder="Find a tag…" class="js-select-menu-tab" role="tab">Tags</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="select-menu-list select-menu-tab-bucket js-select-menu-tab-bucket" data-tab-filter="branches" role="menu">

        <div data-filterable-for="context-commitish-filter-field" data-filterable-type="substring">


            <a class="select-menu-item js-navigation-item js-navigation-open selected"
               href="/CallToPower/Synchronize.js/blob/master/src/synchronize-min.js"
               data-name="master"
               data-skip-pjax="true"
               rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target js-select-menu-filter-text">
                master
              </span>
            </a>
        </div>

          <div class="select-menu-no-results">Nothing to show</div>
      </div>

      <div class="select-menu-list select-menu-tab-bucket js-select-menu-tab-bucket" data-tab-filter="tags">
        <div data-filterable-for="context-commitish-filter-field" data-filterable-type="substring">


            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.6/src/synchronize-min.js"
              data-name="v1.2.6"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.6">
                v1.2.6
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.5/src/synchronize-min.js"
              data-name="v1.2.5"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.5">
                v1.2.5
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.4/src/synchronize-min.js"
              data-name="v1.2.4"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.4">
                v1.2.4
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.3/src/synchronize-min.js"
              data-name="v1.2.3"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.3">
                v1.2.3
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.2/src/synchronize-min.js"
              data-name="v1.2.2"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.2">
                v1.2.2
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.1/src/synchronize-min.js"
              data-name="v1.2.1"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.1">
                v1.2.1
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.2.0/src/synchronize-min.js"
              data-name="v1.2.0"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.2.0">
                v1.2.0
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.1.3/src/synchronize-min.js"
              data-name="v1.1.3"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.1.3">
                v1.1.3
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.1.2/src/synchronize-min.js"
              data-name="v1.1.2"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.1.2">
                v1.1.2
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.1.1/src/synchronize-min.js"
              data-name="v1.1.1"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.1.1">
                v1.1.1
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.1.0/src/synchronize-min.js"
              data-name="v1.1.0"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.1.0">
                v1.1.0
              </span>
            </a>
            <a class="select-menu-item js-navigation-item js-navigation-open "
              href="/CallToPower/Synchronize.js/tree/v1.0.0/src/synchronize-min.js"
              data-name="v1.0.0"
              data-skip-pjax="true"
              rel="nofollow">
              <svg aria-hidden="true" class="octicon octicon-check select-menu-item-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M12 5l-8 8-4-4 1.5-1.5L4 10l6.5-6.5z"/></svg>
              <span class="select-menu-item-text css-truncate-target" title="v1.0.0">
                v1.0.0
              </span>
            </a>
        </div>

        <div class="select-menu-no-results">Nothing to show</div>
      </div>

    </div>
  </div>
</div>

  <div class="BtnGroup float-right">
    <a href="/CallToPower/Synchronize.js/find/master"
          class="js-pjax-capture-input btn btn-sm BtnGroup-item"
          data-pjax
          data-hotkey="t">
      Find file
    </a>
    <button aria-label="Copy file path to clipboard" class="js-zeroclipboard btn btn-sm BtnGroup-item tooltipped tooltipped-s" data-copied-hint="Copied!" type="button">Copy path</button>
  </div>
  <div class="breadcrumb js-zeroclipboard-target">
    <span class="repo-root js-repo-root"><span class="js-path-segment"><a href="/CallToPower/Synchronize.js"><span>Synchronize.js</span></a></span></span><span class="separator">/</span><span class="js-path-segment"><a href="/CallToPower/Synchronize.js/tree/master/src"><span>src</span></a></span><span class="separator">/</span><strong class="final-path">synchronize-min.js</strong>
  </div>
</div>


  <div class="commit-tease">
      <span class="float-right">
        <a class="commit-tease-sha" href="/CallToPower/Synchronize.js/commit/b10d4b289c4dff37a26a97ac3c65f34e4b588ab2" data-pjax>
          b10d4b2
        </a>
        <relative-time datetime="2016-04-23T17:47:20Z">Apr 24, 2016</relative-time>
      </span>
      <div>
        <img alt="@CallToPower" class="avatar" height="20" src="https://avatars2.githubusercontent.com/u/20119?v=3&amp;s=40" width="20" />
        <a href="/CallToPower" class="user-mention" rel="author">CallToPower</a>
          <a href="/CallToPower/Synchronize.js/commit/b10d4b289c4dff37a26a97ac3c65f34e4b588ab2" class="message" data-pjax="true" title="Heavy bug fixing and performance optimizations">Heavy bug fixing and performance optimizations</a>
      </div>

    <div class="commit-tease-contributors">
      <button type="button" class="btn-link muted-link contributors-toggle" data-facebox="#blob_contributors_box">
        <strong>1</strong>
         contributor
      </button>
      
    </div>

    <div id="blob_contributors_box" style="display:none">
      <h2 class="facebox-header" data-facebox-id="facebox-header">Users who have contributed to this file</h2>
      <ul class="facebox-user-list" data-facebox-id="facebox-description">
          <li class="facebox-user-list-item">
            <img alt="@CallToPower" height="24" src="https://avatars0.githubusercontent.com/u/20119?v=3&amp;s=48" width="24" />
            <a href="/CallToPower">CallToPower</a>
          </li>
      </ul>
    </div>
  </div>


<div class="file">
  <div class="file-header">
  <div class="file-actions">

    <div class="BtnGroup">
      <a href="/CallToPower/Synchronize.js/raw/master/src/synchronize-min.js" class="btn btn-sm BtnGroup-item" id="raw-url">Raw</a>
        <a href="/CallToPower/Synchronize.js/blame/master/src/synchronize-min.js" class="btn btn-sm js-update-url-with-hash BtnGroup-item" data-hotkey="b">Blame</a>
      <a href="/CallToPower/Synchronize.js/commits/master/src/synchronize-min.js" class="btn btn-sm BtnGroup-item" rel="nofollow">History</a>
    </div>

        <a class="btn-octicon tooltipped tooltipped-nw"
           href="https://windows.github.com"
           aria-label="Open this file in GitHub Desktop"
           data-ga-click="Repository, open with desktop, type:windows">
            <svg aria-hidden="true" class="octicon octicon-device-desktop" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path fill-rule="evenodd" d="M15 2H1c-.55 0-1 .45-1 1v9c0 .55.45 1 1 1h5.34c-.25.61-.86 1.39-2.34 2h8c-1.48-.61-2.09-1.39-2.34-2H15c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zm0 9H1V3h14v8z"/></svg>
        </a>

        <button type="button" class="btn-octicon disabled tooltipped tooltipped-nw"
          aria-label="You must be signed in to make or propose changes">
          <svg aria-hidden="true" class="octicon octicon-pencil" height="16" version="1.1" viewBox="0 0 14 16" width="14"><path fill-rule="evenodd" d="M0 12v3h3l8-8-3-3-8 8zm3 2H1v-2h1v1h1v1zm10.3-9.3L12 6 9 3l1.3-1.3a.996.996 0 0 1 1.41 0l1.59 1.59c.39.39.39 1.02 0 1.41z"/></svg>
        </button>
        <button type="button" class="btn-octicon btn-octicon-danger disabled tooltipped tooltipped-nw"
          aria-label="You must be signed in to make or propose changes">
          <svg aria-hidden="true" class="octicon octicon-trashcan" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M11 2H9c0-.55-.45-1-1-1H5c-.55 0-1 .45-1 1H2c-.55 0-1 .45-1 1v1c0 .55.45 1 1 1v9c0 .55.45 1 1 1h7c.55 0 1-.45 1-1V5c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zm-1 12H3V5h1v8h1V5h1v8h1V5h1v8h1V5h1v9zm1-10H2V3h9v1z"/></svg>
        </button>
  </div>

  <div class="file-info">
      20 lines (18 sloc)
      <span class="file-info-divider"></span>
    11.4 KB
  </div>
</div>

  

  <div itemprop="text" class="blob-wrapper data type-javascript">
      <table class="highlight tab-size js-file-line-container" data-tab-size="8">
      <tr>
        <td id="L1" class="blob-num js-line-number" data-line-number="1"></td>
        <td id="LC1" class="blob-code blob-code-inner js-file-line"><span class="pl-c"><span class="pl-c">/**</span></span></td>
      </tr>
      <tr>
        <td id="L2" class="blob-num js-line-number" data-line-number="2"></td>
        <td id="LC2" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * Synchronize.js</span></td>
      </tr>
      <tr>
        <td id="L3" class="blob-num js-line-number" data-line-number="3"></td>
        <td id="LC3" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * Compiled on 23.04.2016</span></td>
      </tr>
      <tr>
        <td id="L4" class="blob-num js-line-number" data-line-number="4"></td>
        <td id="LC4" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * Version 1.2.6</span></td>
      </tr>
      <tr>
        <td id="L5" class="blob-num js-line-number" data-line-number="5"></td>
        <td id="LC5" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> *</span></td>
      </tr>
      <tr>
        <td id="L6" class="blob-num js-line-number" data-line-number="6"></td>
        <td id="LC6" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> *  Copyright (C) 2013-2016 Denis Meyer, <EMAIL></span></td>
      </tr>
      <tr>
        <td id="L7" class="blob-num js-line-number" data-line-number="7"></td>
        <td id="LC7" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * All rights reserved.</span></td>
      </tr>
      <tr>
        <td id="L8" class="blob-num js-line-number" data-line-number="8"></td>
        <td id="LC8" class="blob-code blob-code-inner js-file-line"><span class="pl-c"></span></td>
      </tr>
      <tr>
        <td id="L9" class="blob-num js-line-number" data-line-number="9"></td>
        <td id="LC9" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:</span></td>
      </tr>
      <tr>
        <td id="L10" class="blob-num js-line-number" data-line-number="10"></td>
        <td id="LC10" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.</span></td>
      </tr>
      <tr>
        <td id="L11" class="blob-num js-line-number" data-line-number="11"></td>
        <td id="LC11" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.</span></td>
      </tr>
      <tr>
        <td id="L12" class="blob-num js-line-number" data-line-number="12"></td>
        <td id="LC12" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * 3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.</span></td>
      </tr>
      <tr>
        <td id="L13" class="blob-num js-line-number" data-line-number="13"></td>
        <td id="LC13" class="blob-code blob-code-inner js-file-line"><span class="pl-c"></span></td>
      </tr>
      <tr>
        <td id="L14" class="blob-num js-line-number" data-line-number="14"></td>
        <td id="LC14" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,</span></td>
      </tr>
      <tr>
        <td id="L15" class="blob-num js-line-number" data-line-number="15"></td>
        <td id="LC15" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS</span></td>
      </tr>
      <tr>
        <td id="L16" class="blob-num js-line-number" data-line-number="16"></td>
        <td id="LC16" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE</span></td>
      </tr>
      <tr>
        <td id="L17" class="blob-num js-line-number" data-line-number="17"></td>
        <td id="LC17" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></td>
      </tr>
      <tr>
        <td id="L18" class="blob-num js-line-number" data-line-number="18"></td>
        <td id="LC18" class="blob-code blob-code-inner js-file-line"><span class="pl-c"> * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></td>
      </tr>
      <tr>
        <td id="L19" class="blob-num js-line-number" data-line-number="19"></td>
        <td id="LC19" class="blob-code blob-code-inner js-file-line"><span class="pl-c"><span class="pl-c">*/</span></span></td>
      </tr>
      <tr>
        <td id="L20" class="blob-num js-line-number" data-line-number="20"></td>
        <td id="LC20" class="blob-code blob-code-inner js-file-line">!function(a){function b(a){ga&amp;&amp;window.console&amp;&amp;console.log(a)}function c(a,b,c){return!isNaN(a)&amp;&amp;!isNaN(b)&amp;&amp;!isNaN(c)&amp;&amp;c&gt;=b?a&gt;=b&amp;&amp;c&gt;=a:!1}function d(c){return c?f()?videojs(c):a(&quot;#&quot;+c):void b(&quot;SJS: [getVideoObj] Undefined video element id &#39;&quot;+c+&quot;&#39;&quot;)}function e(a){return a?f()?videojs(a):d(a).get(0):void b(&quot;SJS: [getVideo] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;)}function f(){return&quot;undefined&quot;!=typeof videojs}function g(a){if(f()&amp;&amp;a){var b=a.id();return&quot;&quot;!==b?b:a}return a}function h(a){return n(a)?G()?ma?(b(&quot;SJS: [play] A video is currently buffering&quot;),!1):a?(f()?e(a).paused()&amp;&amp;e(a).ended()||(b(&quot;SJS: [play] Playing video element id &#39;&quot;+a+&quot;&#39;&quot;),e(a).play()):(e(a).paused||!e(a).ended)&amp;&amp;(b(&quot;SJS: [play] Playing video element id &#39;&quot;+a+&quot;&#39;&quot;),e(a).play()),!0):(b(&quot;SJS: [play] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1):(b(&quot;SJS: [play] Not all video IDs are ready, yet&quot;),!1):!1}function i(a){if(!a)return void b(&quot;SJS: [mute] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;);var c=e(a);f()?c.volume()&gt;0&amp;&amp;(b(&quot;SJS: [mute] Muting video element id &#39;&quot;+a+&quot;&#39;&quot;),c.volume(0)):c.muted||(b(&quot;SJS: [mute] Muting video element id &#39;&quot;+a+&quot;&#39;&quot;),c.muted=!0)}function j(a,c){if(a&amp;&amp;c){b(&quot;SJS: [unmute] Unmuting video element id &#39;&quot;+a+&quot;&#39;&quot;);var d=e(a);return f()?d.volume(c):(d.muted=!1,d.volume(c)),!0}return b(&quot;SJS: [unmute] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1}function k(a){return a?(b(&quot;SJS: [volume] Getting volume from video element id &#39;&quot;+a+&quot;&#39;: &quot;+e(a).volume()),e(a).volume()):(b(&quot;SJS: [volume] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),-1)}function l(a){return a?(b(&quot;SJS: [pause] Pausing video element id &#39;&quot;+a+&quot;&#39;&quot;),e(a).pause()):(b(&quot;SJS: [pause] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1)}function m(a){for(var b=0;b&lt;ha.length;++b)(a&amp;&amp;ha[b]===P||ha[b]!==P)&amp;&amp;l(ha[b])}function n(a){return a?f()?(a===P&amp;&amp;console.error(&quot;IS PAUSED&quot;,e(a).paused()),e(a).paused()):e(a).paused:(b(&quot;SJS: [isPaused] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1)}function o(a){return a?f()?e(a).duration():e(a).duration:(b(&quot;SJS: [getDuration] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),-1)}function p(a){return a?f()?e(a).currentTime():e(a).currentTime:(b(&quot;SJS: [getCurrentTime] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),-1)}function q(a,c){if(a){var d=o(a);return-1!==d&amp;&amp;!isNaN(c)&amp;&amp;c&gt;=0&amp;&amp;d&gt;=c?(f()?e(a).currentTime(c):e(a).currentTime=c,!0):(b(&quot;SJS: [setCurrentTime] Could not set time for video element id &#39;&quot;+a+&quot;&#39;&quot;),q(a,d),!1)}return b(&quot;SJS: [setCurrentTime] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1}function r(a){return a?f()?e(a).playbackRate():e(a).playbackRate:(b(&quot;SJS: [getPlaybackRate] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),1)}function s(a,c){return a?(f()?e(a).playbackRate(c):e(a).playbackRate=c,!0):(b(&quot;SJS: [setPlaybackRate] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;),!1)}function t(a){return a?f()?e(a).buffered():e(a).buffered:void b(&quot;SJS: [getBufferTimeRange] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;)}function u(a){var b=p(P),d=p(a);return-1===b||-1===d||c(d,b-Y,b)?0:d-b}function v(){ha=[],ia={},ja={},ka=0,P=null,la=0,ma=!1,na=!1,oa=!1,Q=null,pa=!1,qa=!1,sa=!1,ra=!1,ta=null,ua=1e4,va=!1,wa=null,xa=[],ya=!1}function w(){if(ha.length&lt;=1)return!1;for(var a=k(P),b=0;b&lt;ha.length;++b)if(ha[b]!==P){d(P).off(),D(b);break}return B(),j(P,a),!0}function x(c){if(b(&quot;Unsynchronizing video with id &quot;+c),c===P){var e=d(P);if(e&amp;&amp;(e.off(&quot;play&quot;),e.off(&quot;pause&quot;),e.off(&quot;ratechange&quot;),e.off(&quot;ended&quot;),e.off(&quot;timeupdate&quot;)),!w())return void z()}for(var f=0;f&lt;ha.length;++f)if(ha[f]===c){ha.splice(f,1),a(document).trigger(&quot;sjs:idUnregistered&quot;,[c]);break}B(),A()}function y(c){b(&quot;Adding video with id &quot;+c+&quot; to synchronization&quot;);for(var d=0;d&lt;ha.length;++d)if(ha[d]===c)return;ha.push(c),a(document).trigger(&quot;sjs:idRegistered&quot;,[c]),A()}function z(){b(&quot;Unsynchronizing all videos&quot;);var a=d(P);a&amp;&amp;(a.off(&quot;play&quot;),a.off(&quot;pause&quot;),a.off(&quot;ratechange&quot;),a.off(&quot;ended&quot;),a.off(&quot;timeupdate&quot;)),v()}function A(){W=Date.now();for(var c=0;c&lt;ha.length;++c)if(ha[c]!==P){var d=!1,e=u(ha[c]);i(ha[c]);var f=n(P);if(f)d=!0;else if(ya)ya&amp;&amp;(Math.abs(e)&gt;ea&amp;&amp;Math.abs(e)&gt;ba?d=!0:n(P)||xa[ha[c]]||(b(&quot;SJS: [synchronize] Playing video element id &#39;&quot;+ha[c]+&quot;&#39;&quot;),h(ha[c])));else{var g=r(ha[c]),j=r(P);e&gt;ca?Math.abs(e)&lt;Z?(a(document).trigger(&quot;sjs:synchronizing&quot;,[p(P),ha[c]]),g!==j&amp;&amp;(b(&quot;SJS: [synchronize] Decreasing playback rate of video element id &#39;&quot;+ha[c]+&quot;&#39; from &quot;+g+&quot; to &quot;+(j+_)),s(ha[c],j+_))):(a(document).trigger(&quot;sjs:synchronizing&quot;,[p(P),ha[c]]),s(ha[c],r(P)),d=!0):da&gt;e?Math.abs(e)&lt;Z?(a(document).trigger(&quot;sjs:synchronizing&quot;,[p(P),ha[c]]),g!==j&amp;&amp;(b(&quot;SJS: [synchronize] Increasing playback rate of video element id &#39;&quot;+ha[c]+&quot;&#39; from &quot;+g+&quot; to &quot;+(j+$)),s(ha[c],j+$))):(a(document).trigger(&quot;sjs:synchronizing&quot;,[p(P),ha[c]]),s(ha[c],r(P)),d=!0):n(P)||xa[ha[c]]||(s(ha[c],r(P)),b(&quot;SJS: [synchronize] Playing video element id &#39;&quot;+ha[c]+&quot;&#39;&quot;),h(ha[c]))}d&amp;&amp;(a(document).trigger(&quot;sjs:synchronizing&quot;,[p(P),ha[c]]),f?(b(&quot;SJS: [synchronize] Seeking video element id &#39;&quot;+ha[c]+&quot;&#39;: &quot;+p(P)),q(ha[c],p(P))&amp;&amp;(b(&quot;SJS: [synchronize] Pausing video element id &#39;&quot;+ha[c]+&quot;&#39; after seeking&quot;),l(ha[c]))):(b(&quot;SJS: [synchronize] Seeking video element id &#39;&quot;+ha[c]+&quot;&#39;: &quot;+(p(P)+aa)),q(ha[c],p(P)+aa)&amp;&amp;(h(ha[c]),xa[ha[c]]||(b(&quot;SJS: [synchronize] Playing video element id &#39;&quot;+ha[c]+&quot;&#39; after seeking&quot;),h(ha[c])))))}}function B(){if(F()){var c=d(P);ya=0!==a(&quot;#&quot;+P+&quot;_flash_api&quot;).length,c.on(&quot;play&quot;,function(){b(&quot;SJS: Master received &#39;play&#39; event&quot;),a(document).trigger(&quot;sjs:masterPlay&quot;,[p(P)]),sa=!1,!oa&amp;&amp;R&amp;&amp;(oa=!0,C());for(var c=0;c&lt;ha.length;++c)ha[c]!==P&amp;&amp;(h(ha[c]),i(ha[c]))}),c.on(&quot;pause&quot;,function(){b(&quot;SJS: Master received &#39;pause&#39; event&quot;),a(document).trigger(&quot;sjs:masterPause&quot;,[p(P)]),sa=!qa,qa=!1,m(!0)}),c.on(&quot;ratechange&quot;,function(){b(&quot;SJS: Master received &#39;ratechange&#39; event&quot;),a(document).trigger(&quot;sjs:masterPlaybackRateChanged&quot;,[r(P)]);for(var c=0;c&lt;ha.length;++c)ha[c]!==P&amp;&amp;s(ha[c],r(P))}),c.on(&quot;ended&quot;,function(){b(&quot;SJS: Master received &#39;ended&#39; event&quot;),a(document).trigger(&quot;sjs:masterEnded&quot;,[o(P)]),sa=!0,m(!1),A()}),c.on(&quot;timeupdate&quot;,function(){a(document).trigger(&quot;sjs:masterTimeupdate&quot;,[p(P)]),sa=sa||!n(P),(Date.now()-W&gt;=X||n(P))&amp;&amp;A()})}else m(!0)}function C(){Q=window.setInterval(function(){var b,d=!0;for(b=0;b&lt;ha.length;++b){var e=t(ha[b]);if(e){for(var f=o(ha[b]),g=p(ha[b])+U,i=!1,j=0;j&lt;e.length&amp;&amp;!i;++j)g=g&gt;=f?f:g,c(g,e.start(j),e.end(j)+fa)&amp;&amp;(i=!0);d=d&amp;&amp;i,ma=!d}}d?pa&amp;&amp;!sa?(pa=!1,h(P),sa=!1,a(document).trigger(&quot;sjs:bufferedAndAutoplaying&quot;,[])):pa&amp;&amp;(pa=!1,a(document).trigger(&quot;sjs:bufferedButNotAutoplaying&quot;,[])):(pa=!0,qa=!0,m(!0),a(document).trigger(&quot;sjs:buffering&quot;,[]))},S)}function D(b){ka=b&lt;ha.length?b:0,P=ha[ka],a(document).trigger(&quot;sjs:masterSet&quot;,[P])}function E(a,c){&quot;&quot;!==a?d(a).on(&quot;loadeddata&quot;,function(){va=!0,c&amp;&amp;c()}):b(&quot;SJS: [doWhenDataLoaded] Undefined video element id &#39;&quot;+a+&quot;&#39;&quot;)}function F(){if(f()){for(var a=0;a&lt;ha.length;++a)if(!ja[ha[a]])return!1;return!0}return la===ha.length}function G(){if(f()){for(var a=0;a&lt;ha.length;++a)if(!ia[ha[a]])return!1;return!0}return la===ha.length}function H(){m(!0),na=!0}function I(){m(!0),na=!1}function J(){null!==ta&amp;&amp;(window.clearInterval(ta),ta=null)}function K(b){var c;if(f())for(c=0;c&lt;ha.length;++c)a(document).trigger(&quot;sjs:idRegistered&quot;,[ha[c]]),d(ha[c]).on(&quot;play&quot;,H),d(ha[c]).on(&quot;pause&quot;,I),d(ha[c]).ready(function(){var c=g(this);ia[c]=!0,E(c,function(){if(ja[c]=!0,a(document).trigger(&quot;sjs:playerLoaded&quot;,[c]),F()){D(b);for(var e=0;e&lt;ha.length;++e)d(ha[e]).off(&quot;play&quot;,H),d(ha[e]).off(&quot;pause&quot;,I);B(),na&amp;&amp;h(P),a(document).trigger(&quot;sjs:allPlayersReady&quot;,[])}}),wa=window.setInterval(function(){if(va)window.clearInterval(wa),wa=null;else for(var a=0;a&lt;ha.length;++a)d(ha[a]).trigger(&quot;loadeddata&quot;)},T)});else for(c=0;c&lt;ha.length;++c)a(document).trigger(&quot;sjs:idRegistered&quot;,[ha[c]]),d(ha[c]).on(&quot;play&quot;,H),d(ha[c]).on(&quot;pause&quot;,I),d(ha[c]).ready(function(){if(++la,F()){D(b);for(var c=0;c&lt;ha.length;++c)d(ha[c]).off(&quot;play&quot;,H),d(ha[c]).off(&quot;pause&quot;,I);B(),na&amp;&amp;h(P),a(document).trigger(&quot;sjs:allPlayersReady&quot;,[])}})}function L(){V&amp;&amp;a(document).on(&quot;sjs:buffering&quot;,function(){b(&quot;SJS: Received &#39;sjs:buffering&#39; event&quot;),ta=setInterval(function(){F()&amp;&amp;!sa&amp;&amp;(sa=!1,h(P),J())},ua)}).on(&quot;sjs:bufferedAndAutoplaying&quot;,function(){b(&quot;SJS: Received &#39;sjs:bufferedAndAutoplaying&#39; event&quot;),J()}).on(&quot;sjs:bufferedButNotAutoplaying&quot;,function(){b(&quot;SJS: Received &#39;sjs:bufferedButNotAutoplaying&#39; event&quot;),J()})}function M(){a(document).on(&quot;sjs:play&quot;,function(){b(&quot;SJS: Received &#39;sjs:play&#39; event&quot;),F()&amp;&amp;h(P)}).on(&quot;sjs:pause&quot;,function(){b(&quot;SJS: Received &#39;sjs:pause&#39; event&quot;),F()&amp;&amp;l(P)}).on(&quot;sjs:setCurrentTime&quot;,function(a,c){b(&quot;SJS: Received &#39;sjs:setCurrentTime&#39; event&quot;),F()&amp;&amp;q(P,c)}).on(&quot;sjs:synchronize&quot;,function(){b(&quot;SJS: Received &#39;sjs:synchronize&#39; event&quot;),F()&amp;&amp;A()}).on(&quot;sjs:addToSynch&quot;,function(a,c){b(&quot;SJS: Received &#39;sjs:addToSynch&#39; event&quot;),c&amp;&amp;y(c)}).on(&quot;sjs:removeFromSynch&quot;,function(a,c){b(&quot;SJS: Received &#39;sjs:removeFromSynch&#39; event&quot;),c&amp;&amp;x(c)}).on(&quot;sjs:unsynchronize&quot;,function(){b(&quot;SJS: Received &#39;sjs:unsynchronize&#39; event&quot;),z()}).on(&quot;sjs:startBufferChecker&quot;,function(){b(&quot;SJS: Received &#39;sjs:startBufferChecker&#39; event&quot;),oa||(window.clearInterval(Q),oa=!0,C())}).on(&quot;sjs:stopBufferChecker&quot;,function(){b(&quot;SJS: Received &#39;sjs:stopBufferChecker&#39; event&quot;),window.clearInterval(Q),oa=!1,ma=!1})}function N(b,c,d){var e,g=!0;if(2===d.length){var h=a(&#39;video[mediagroup=&quot;&#39;+c+&#39;&quot;]&#39;);for(e=0;e&lt;h.length;++e){var i=ha.length;ha[i]=h[e].getAttribute(&quot;id&quot;);var j=&quot;_html5_api&quot;;ha[i]=f()&amp;&amp;-1!==ha[i].indexOf(j)?ha[i].substr(0,ha[i].length-j.length):ha[i],ia[ha[e-1]]=!1,ja[ha[e-1]]=!1}}else for(ka=b,e=1;e&lt;d.length;++e)g=g&amp;&amp;d[e]&amp;&amp;a(&quot;#&quot;+d[e]).length,g?(ha[ha.length]=d[e],ia[ha[e-1]]=!1,ja[ha[e-1]]=!1):a(document).trigger(&quot;sjs:invalidId&quot;,[d[e]]);return g}function O(c,d){var e=N(c,d,arguments);e&amp;&amp;ha.length&gt;1?K(c):(b(&quot;SJS: Not enough videos&quot;),a(document).trigger(&quot;sjs:notEnoughVideos&quot;,[])),L(),M()}var P,Q,R=!0,S=1e3,T=5e3,U=2,V=!0,W=0,X=1e3,Y=1,Z=4,$=.5,_=-.5,aa=.25,ba=aa+.05,ca=.05,da=-.05,ea=.05,fa=.3,ga=!1,ha=[],ia={},ja={},ka=0,la=0,ma=!1,na=!1,oa=!1,pa=!1,qa=!1,ra=!1,sa=!1,ta=null,ua=1e4,va=!1,wa=null,xa=[],ya=!1;a.synchronizeVideos=O,a(document).on(&quot;sjs:debug&quot;,function(a,c){ga=c,b(&quot;SJS: Received &#39;sjs:debug&#39; event&quot;)})}(jQuery);</td>
      </tr>
</table>

  </div>

</div>

<button type="button" data-facebox="#jump-to-line" data-facebox-class="linejump" data-hotkey="l" class="d-none">Jump to Line</button>
<div id="jump-to-line" style="display:none">
  <!-- '"` --><!-- </textarea></xmp> --></option></form><form accept-charset="UTF-8" action="" class="js-jump-to-line-form" method="get"><div style="margin:0;padding:0;display:inline"><input name="utf8" type="hidden" value="&#x2713;" /></div>
    <input class="form-control linejump-input js-jump-to-line-field" type="text" placeholder="Jump to line&hellip;" aria-label="Jump to line" autofocus>
    <button type="submit" class="btn">Go</button>
</form></div>

  </div>
  <div class="modal-backdrop js-touch-events"></div>
</div>


    </div>
  </div>

  </div>

      <div class="container site-footer-container">
  <div class="site-footer" role="contentinfo">
    <ul class="site-footer-links float-right">
        <li><a href="https://github.com/contact" data-ga-click="Footer, go to contact, text:contact">Contact GitHub</a></li>
      <li><a href="https://developer.github.com" data-ga-click="Footer, go to api, text:api">API</a></li>
      <li><a href="https://training.github.com" data-ga-click="Footer, go to training, text:training">Training</a></li>
      <li><a href="https://shop.github.com" data-ga-click="Footer, go to shop, text:shop">Shop</a></li>
        <li><a href="https://github.com/blog" data-ga-click="Footer, go to blog, text:blog">Blog</a></li>
        <li><a href="https://github.com/about" data-ga-click="Footer, go to about, text:about">About</a></li>

    </ul>

    <a href="https://github.com" aria-label="Homepage" class="site-footer-mark" title="GitHub">
      <svg aria-hidden="true" class="octicon octicon-mark-github" height="24" version="1.1" viewBox="0 0 16 16" width="24"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>
</a>
    <ul class="site-footer-links">
      <li>&copy; 2017 <span title="0.06613s from github-fe120-cp1-prd.iad.github.net">GitHub</span>, Inc.</li>
        <li><a href="https://github.com/site/terms" data-ga-click="Footer, go to terms, text:terms">Terms</a></li>
        <li><a href="https://github.com/site/privacy" data-ga-click="Footer, go to privacy, text:privacy">Privacy</a></li>
        <li><a href="https://github.com/security" data-ga-click="Footer, go to security, text:security">Security</a></li>
        <li><a href="https://status.github.com/" data-ga-click="Footer, go to status, text:status">Status</a></li>
        <li><a href="https://help.github.com" data-ga-click="Footer, go to help, text:help">Help</a></li>
    </ul>
  </div>
</div>



  

  <div id="ajax-error-message" class="ajax-error-message flash flash-error">
    <svg aria-hidden="true" class="octicon octicon-alert" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path fill-rule="evenodd" d="M8.865 1.52c-.18-.31-.51-.5-.87-.5s-.69.19-.87.5L.275 13.5c-.18.31-.18.69 0 1 .**********.87.5h13.7c.36 0 .69-.19.86-.5.17-.31.18-.69.01-1L8.865 1.52zM8.995 13h-2v-2h2v2zm0-3h-2V6h2v4z"/></svg>
    <button type="button" class="flash-close js-flash-close js-ajax-error-dismiss" aria-label="Dismiss error">
      <svg aria-hidden="true" class="octicon octicon-x" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"/></svg>
    </button>
    You can't perform that action at this time.
  </div>


    
    <script crossorigin="anonymous" integrity="sha256-UGFpyy/nYlS5IejJRN1AblyrLXGeZX6s6K2phIYjFHI=" src="https://assets-cdn.github.com/assets/frameworks-506169cb2fe76254b921e8c944dd406e5cab2d719e657eace8ada98486231472.js"></script>
    <script async="async" crossorigin="anonymous" integrity="sha256-i27dVdFxKeJquexWC35e0pCOS6u4HQL4wilBne5YwME=" src="https://assets-cdn.github.com/assets/github-8b6edd55d17129e26ab9ec560b7e5ed2908e4babb81d02f8c229419dee58c0c1.js"></script>
    
    
    
    
  <div class="js-stale-session-flash stale-session-flash flash flash-warn flash-banner d-none">
    <svg aria-hidden="true" class="octicon octicon-alert" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path fill-rule="evenodd" d="M8.865 1.52c-.18-.31-.51-.5-.87-.5s-.69.19-.87.5L.275 13.5c-.18.31-.18.69 0 1 .**********.87.5h13.7c.36 0 .69-.19.86-.5.17-.31.18-.69.01-1L8.865 1.52zM8.995 13h-2v-2h2v2zm0-3h-2V6h2v4z"/></svg>
    <span class="signed-in-tab-flash">You signed in with another tab or window. <a href="">Reload</a> to refresh your session.</span>
    <span class="signed-out-tab-flash">You signed out in another tab or window. <a href="">Reload</a> to refresh your session.</span>
  </div>
  <div class="facebox" id="facebox" style="display:none;">
  <div class="facebox-popup">
    <div class="facebox-content" role="dialog" aria-labelledby="facebox-header" aria-describedby="facebox-description">
    </div>
    <button type="button" class="facebox-close js-facebox-close" aria-label="Close modal">
      <svg aria-hidden="true" class="octicon octicon-x" height="16" version="1.1" viewBox="0 0 12 16" width="12"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"/></svg>
    </button>
  </div>
</div>


  </body>
</html>


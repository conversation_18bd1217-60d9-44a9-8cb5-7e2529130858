!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,e.LC=t()}}(function(){return function t(e,n,i){function r(s,a){if(!n[s]){if(!e[s]){var h="function"==typeof require&&require;if(!a&&h)return h(s,!0);if(o)return o(s,!0);var u=new Error("Cannot find module '"+s+"'");throw u.code="MODULE_NOT_FOUND",u}var c=n[s]={exports:{}};e[s][0].call(c.exports,function(t){var n=e[s][1][t];return r(n?n:t)},c,c.exports,t,e,n,i)}return n[s].exports}for(var o="function"==typeof require&&require,s=0;s<i.length;s++)r(i[s]);return r}({1:[function(t,e,n){var i,r,o,s,a,h,u,c,l,p,d,f,g,y,m,S=function(t,e){return function(){return t.apply(e,arguments)}},x=[].slice,v=[].indexOf||function(t){for(var e=0,n=this.length;n>e;e++)if(e in this&&this[e]===t)return e;return-1};a=t("./actions"),h=t("./bindEvents"),c=t("./math"),l=t("./shapes"),u=l.createShape,y=l.shapeToJSON,r=l.JSONToShape,p=t("./canvasRenderer").renderShapeToContext,d=t("./svgRenderer").renderShapeToSVG,f=t("./renderSnapshotToImage"),g=t("./renderSnapshotToSVG"),s=t("../tools/Pencil"),m=t("./util"),i="infinite",e.exports=o=function(){function t(t,e){this.setImageSize=S(this.setImageSize,this);var n,r;r=null,n=null,t instanceof HTMLElement?(n=t,r=e):r=t,this.opts=r||{},this.config={zoomMin:r.zoomMin||.2,zoomMax:r.zoomMax||4,zoomStep:r.zoomStep||.2},this.colors={primary:r.primaryColor||"#000",secondary:r.secondaryColor||"#fff",background:r.backgroundColor||"transparent"},this.watermarkImage=r.watermarkImage,this.watermarkScale=r.watermarkScale||1,this.backgroundCanvas=document.createElement("canvas"),this.backgroundCtx=this.backgroundCanvas.getContext("2d"),this.canvas=document.createElement("canvas"),this.canvas.style["background-color"]="transparent",this.buffer=document.createElement("canvas"),this.buffer.style["background-color"]="transparent",this.ctx=this.canvas.getContext("2d"),this.bufferCtx=this.buffer.getContext("2d"),this.backingScale=m.getBackingScale(this.ctx),this.backgroundShapes=r.backgroundShapes||[],this._shapesInProgress=[],this.shapes=[],this.undoStack=[],this.redoStack=[],this.isDragging=!1,this.position={x:0,y:0},this.scale=1,this.setTool(new this.opts.tools[0](this)),this.width=r.imageSize.width||i,this.height=r.imageSize.height||i,this.setZoom(this.scale),r.snapshot&&this.loadSnapshot(r.snapshot),this.isBound=!1,n&&this.bindToElement(n),this.respondToSizeChange=function(){}}return t.prototype.bindToElement=function(t){var e,n;return this.containerEl?void console.warn("Trying to bind Literally Canvas to a DOM element more than once is unsupported."):(this.containerEl=t,this._unsubscribeEvents=h(this,this.containerEl,this.opts.keyboardShortcuts),this.containerEl.style["background-color"]=this.colors.background,this.containerEl.appendChild(this.backgroundCanvas),this.containerEl.appendChild(this.canvas),this.isBound=!0,n=function(t){return function(){return t.keepPanInImageBounds(),t.repaintAllLayers()}}(this),this.respondToSizeChange=m.matchElementSize(this.containerEl,[this.backgroundCanvas,this.canvas],this.backingScale,n),this.watermarkImage&&(this.watermarkImage.onload=function(t){return function(){return t.repaintLayer("background")}}(this)),null!=(e=this.tool)&&e.didBecomeActive(this),n())},t.prototype._teardown=function(){var t;return null!=(t=this.tool)&&t.willBecomeInactive(this),"function"==typeof this._unsubscribeEvents&&this._unsubscribeEvents(),this.tool=null,this.containerEl=null,this.isBound=!1},t.prototype.trigger=function(t,e){return this.canvas.dispatchEvent(new CustomEvent(t,{detail:e})),null},t.prototype.on=function(t,e){var n;return n=function(t){return e(t.detail)},this.canvas.addEventListener(t,n),function(e){return function(){return e.canvas.removeEventListener(t,n)}}(this)},t.prototype.getRenderScale=function(){return this.scale*this.backingScale},t.prototype.clientCoordsToDrawingCoords=function(t,e){return{x:(t*this.backingScale-this.position.x)/this.getRenderScale(),y:(e*this.backingScale-this.position.y)/this.getRenderScale()}},t.prototype.drawingCoordsToClientCoords=function(t,e){return{x:t*this.getRenderScale()+this.position.x,y:e*this.getRenderScale()+this.position.y}},t.prototype.setImageSize=function(t,e){return this.width=t||i,this.height=e||i,this.keepPanInImageBounds(),this.repaintAllLayers(),this.trigger("imageSizeChange",{width:this.width,height:this.height})},t.prototype.setTool=function(t){var e;return this.isBound&&null!=(e=this.tool)&&e.willBecomeInactive(this),this.tool=t,this.trigger("toolChange",{tool:t}),this.isBound?this.tool.didBecomeActive(this):void 0},t.prototype.setShapesInProgress=function(t){return this._shapesInProgress=t},t.prototype.pointerDown=function(t,e){var n;return n=this.clientCoordsToDrawingCoords(t,e),this.tool.usesSimpleAPI?(this.tool.begin(n.x,n.y,this),this.isDragging=!0,this.trigger("drawStart",{tool:this.tool})):(this.isDragging=!0,this.trigger("lc-pointerdown",{tool:this.tool,x:n.x,y:n.y,rawX:t,rawY:e}))},t.prototype.pointerMove=function(t,e){return m.requestAnimationFrame(function(n){return function(){var i,r;return i=n.clientCoordsToDrawingCoords(t,e),(null!=(r=n.tool)?r.usesSimpleAPI:0)?n.isDragging?(n.tool["continue"](i.x,i.y,n),n.trigger("drawContinue",{tool:n.tool})):void 0:n.isDragging?n.trigger("lc-pointerdrag",{tool:n.tool,x:i.x,y:i.y,rawX:t,rawY:e}):n.trigger("lc-pointermove",{tool:n.tool,x:i.x,y:i.y,rawX:t,rawY:e})}}(this))},t.prototype.pointerUp=function(t,e){var n;return n=this.clientCoordsToDrawingCoords(t,e),this.tool.usesSimpleAPI?this.isDragging?(this.tool.end(n.x,n.y,this),this.isDragging=!1,this.trigger("drawEnd",{tool:this.tool})):void 0:(this.isDragging=!1,this.trigger("lc-pointerup",{tool:this.tool,x:n.x,y:n.y,rawX:t,rawY:e}))},t.prototype.setColor=function(t,e){if(this.colors[t]=e,this.isBound){switch(t){case"background":this.containerEl.style.backgroundColor=this.colors.background,this.repaintLayer("background");break;case"primary":this.repaintLayer("main");break;case"secondary":this.repaintLayer("main")}return this.trigger(t+"ColorChange",this.colors[t]),"background"===t?this.trigger("drawingChange"):void 0}},t.prototype.getColor=function(t){return this.colors[t]},t.prototype.saveShape=function(t,e,n){return null==e&&(e=!0),null==n&&(n=null),n||(n=this.shapes.length?this.shapes[this.shapes.length-1].id:null),this.execute(new a.AddShapeAction(this,t,n)),e&&this.trigger("shapeSave",{shape:t,previousShapeId:n}),this.trigger("drawingChange")},t.prototype.pan=function(t,e){return this.setPan(this.position.x-t,this.position.y-e)},t.prototype.keepPanInImageBounds=function(){var t,e,n,r;return e=this.getRenderScale(),t=this.position,n=t.x,r=t.y,this.width!==i&&(n=this.canvas.width>this.width*e?(this.canvas.width-this.width*e)/2:Math.max(Math.min(0,n),this.canvas.width-this.width*e)),this.height!==i&&(r=this.canvas.height>this.height*e?(this.canvas.height-this.height*e)/2:Math.max(Math.min(0,r),this.canvas.height-this.height*e)),this.position={x:n,y:r}},t.prototype.setPan=function(t,e){return this.position={x:t,y:e},this.keepPanInImageBounds(),this.repaintAllLayers(),this.trigger("pan",{x:this.position.x,y:this.position.y})},t.prototype.zoom=function(t){var e;return e=this.scale+t,e=Math.max(e,this.config.zoomMin),e=Math.min(e,this.config.zoomMax),e=Math.round(100*e)/100,this.setZoom(e)},t.prototype.setZoom=function(t){var e;return e=this.scale,this.scale=t,this.position.x=c.scalePositionScalar(this.position.x,this.canvas.width,e,this.scale),this.position.y=c.scalePositionScalar(this.position.y,this.canvas.height,e,this.scale),this.keepPanInImageBounds(),this.repaintAllLayers(),this.trigger("zoom",{oldScale:e,newScale:this.scale})},t.prototype.setWatermarkImage=function(t){return this.watermarkImage=t,m.addImageOnload(t,function(t){return function(){return t.repaintLayer("background")}}(this)),t.width?this.repaintLayer("background"):void 0},t.prototype.repaintAllLayers=function(){var t,e,n,i;for(i=["background","main"],t=0,n=i.length;n>t;t++)e=i[t],this.repaintLayer(e);return null},t.prototype.repaintLayer=function(t,e){var n;if(null==e&&(e="main"===t),this.isBound){switch(t){case"background":this.backgroundCtx.clearRect(0,0,this.backgroundCanvas.width,this.backgroundCanvas.height),n=function(t){return function(){return t.repaintLayer("background")}}(this),this.watermarkImage&&this._renderWatermark(this.backgroundCtx,!0,n),this.draw(this.backgroundShapes,this.backgroundCtx,n);break;case"main":n=function(t){return function(){return t.repaintLayer("main",!0)}}(this),e&&(this.buffer.width=this.canvas.width,this.buffer.height=this.canvas.height,this.bufferCtx.clearRect(0,0,this.buffer.width,this.buffer.height),this.draw(this.shapes,this.bufferCtx,n)),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.width>0&&this.canvas.height>0&&(this.ctx.fillStyle="#ccc",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.clipped(function(t){return function(){return t.ctx.clearRect(0,0,t.canvas.width,t.canvas.height),t.ctx.drawImage(t.buffer,0,0)}}(this),this.ctx),this.clipped(function(t){return function(){return t.transformed(function(){var e,n,i,r,o;for(i=t._shapesInProgress,r=[],e=0,n=i.length;n>e;e++)o=i[e],r.push(p(t.ctx,o,{bufferCtx:t.bufferCtx,shouldOnlyDrawLatest:!0}));return r},t.ctx,t.bufferCtx)}}(this),this.ctx,this.bufferCtx))}return this.trigger("repaint",{layerKey:t})}},t.prototype._renderWatermark=function(t,e,n){return null==e&&(e=!0),this.watermarkImage.width?(t.save(),t.translate(t.canvas.width/2,t.canvas.height/2),t.scale(this.watermarkScale,this.watermarkScale),e&&t.scale(this.backingScale,this.backingScale),t.drawImage(this.watermarkImage,-this.watermarkImage.width/2,-this.watermarkImage.height/2),t.restore()):void(this.watermarkImage.onload=n)},t.prototype.drawShapeInProgress=function(t){return this.repaintLayer("main",!1),this.clipped(function(e){return function(){return e.transformed(function(){return p(e.ctx,t,{bufferCtx:e.bufferCtx,shouldOnlyDrawLatest:!0})},e.ctx,e.bufferCtx)}}(this),this.ctx,this.bufferCtx)},t.prototype.draw=function(t,e,n){var i;if(t.length)return i=function(i){return function(){var i,r,o,s;for(o=[],i=0,r=t.length;r>i;i++)s=t[i],o.push(p(e,s,{retryCallback:n}));return o}}(this),this.clipped(function(t){return function(){return t.transformed(i,e)}}(this),e)},t.prototype.clipped=function(){var t,e,n,r,o,s,a,h,u,c,l,p;for(n=arguments[0],t=2<=arguments.length?x.call(arguments,1):[],l=this.width===i?0:this.position.x,p=this.height===i?0:this.position.y,c=function(){switch(this.width){case i:return this.canvas.width;default:return this.width*this.getRenderScale()}}.call(this),r=function(){switch(this.height){case i:return this.canvas.height;default:return this.height*this.getRenderScale()}}.call(this),o=0,a=t.length;a>o;o++)e=t[o],e.save(),e.beginPath(),e.rect(l,p,c,r),e.clip();for(n(),u=[],s=0,h=t.length;h>s;s++)e=t[s],u.push(e.restore());return u},t.prototype.transformed=function(){var t,e,n,i,r,o,s,a,h;for(n=arguments[0],t=2<=arguments.length?x.call(arguments,1):[],i=0,o=t.length;o>i;i++)e=t[i],e.save(),e.translate(Math.floor(this.position.x),Math.floor(this.position.y)),h=this.getRenderScale(),e.scale(h,h);for(n(),a=[],r=0,s=t.length;s>r;r++)e=t[r],a.push(e.restore());return a},t.prototype.clear=function(t){var e,n;return null==t&&(t=!0),n=this.shapes,e=[],this.setShapesInProgress([]),this.execute(new a.ClearAction(this,n,e)),this.repaintLayer("main"),t&&this.trigger("clear",null),this.trigger("drawingChange",{})},t.prototype.execute=function(t){return this.undoStack.push(t),t["do"](),this.redoStack=[]},t.prototype.undo=function(){var t;if(this.undoStack.length)return t=this.undoStack.pop(),t.undo(),this.redoStack.push(t),this.trigger("undo",{action:t}),this.trigger("drawingChange",{})},t.prototype.redo=function(){var t;if(this.redoStack.length)return t=this.redoStack.pop(),this.undoStack.push(t),t["do"](),this.trigger("redo",{action:t}),this.trigger("drawingChange",{})},t.prototype.canUndo=function(){return!!this.undoStack.length},t.prototype.canRedo=function(){return!!this.redoStack.length},t.prototype.getPixel=function(t,e){var n,i;return n=this.drawingCoordsToClientCoords(t,e),i=this.ctx.getImageData(n.x,n.y,1,1).data,i[3]?"rgb("+i[0]+", "+i[1]+", "+i[2]+")":null},t.prototype.getContentBounds=function(){return m.getBoundingRect(this.shapes.concat(this.backgroundShapes).map(function(t){return t.getBoundingRect()}),this.width===i?0:this.width,this.height===i?0:this.height)},t.prototype.getDefaultImageRect=function(t,e){var n;return null==t&&(t={width:0,height:0}),null==e&&(e={top:0,right:0,bottom:0,left:0}),m.getDefaultImageRect(function(){var t,e,i,r;for(i=this.shapes.concat(this.backgroundShapes),r=[],t=0,e=i.length;e>t;t++)n=i[t],r.push(n.getBoundingRect(this.ctx));return r}.call(this),t,e)},t.prototype.getImage=function(t){return null==t&&(t={}),null==t.includeWatermark&&(t.includeWatermark=!0),null==t.scaleDownRetina&&(t.scaleDownRetina=!0),null==t.scale&&(t.scale=1),t.scaleDownRetina||(t.scale*=this.backingScale),t.includeWatermark&&(t.watermarkImage=this.watermarkImage,t.watermarkScale=this.watermarkScale,t.scaleDownRetina||(t.watermarkScale*=this.backingScale)),f(this.getSnapshot(),t)},t.prototype.canvasForExport=function(){return this.repaintAllLayers(),m.combineCanvases(this.backgroundCanvas,this.canvas)},t.prototype.canvasWithBackground=function(t){return m.combineCanvases(t,this.canvasForExport())},t.prototype.getSnapshot=function(t){var e,n,i,r,o,s;for(null==t&&(t=null),null==t&&(t=["shapes","imageSize","colors","position","scale","backgroundShapes"]),s={},r=["colors","position","scale"],e=0,i=r.length;i>e;e++)n=r[e],v.call(t,n)>=0&&(s[n]=this[n]);return v.call(t,"shapes")>=0&&(s.shapes=function(){var t,e,n,i;for(n=this.shapes,i=[],t=0,e=n.length;e>t;t++)o=n[t],i.push(y(o));return i}.call(this)),v.call(t,"backgroundShapes")>=0&&(s.backgroundShapes=function(){var t,e,n,i;for(n=this.backgroundShapes,i=[],t=0,e=n.length;e>t;t++)o=n[t],i.push(y(o));return i}.call(this)),v.call(t,"imageSize")>=0&&(s.imageSize={width:this.width,height:this.height}),s},t.prototype.getSnapshotJSON=function(){return console.warn("lc.getSnapshotJSON() is deprecated. use JSON.stringify(lc.getSnapshot()) instead."),JSON.stringify(this.getSnapshot())},t.prototype.getSVGString=function(t){return null==t&&(t={}),g(this.getSnapshot(),t)},t.prototype.loadSnapshot=function(t){var e,n,i,o,s,h,u,c,l,p;if(t){if(t.colors)for(h=["primary","secondary","background"],e=0,o=h.length;o>e;e++)i=h[e],this.setColor(i,t.colors[i]);if(t.shapes)for(this.shapes=[],u=t.shapes,n=0,s=u.length;s>n;n++)p=u[n],l=r(p),l&&this.execute(new a.AddShapeAction(this,l));return t.backgroundShapes&&(this.backgroundShapes=function(){var e,n,i,o;for(i=t.backgroundShapes,o=[],e=0,n=i.length;n>e;e++)c=i[e],o.push(r(c));return o}()),t.imageSize&&(this.width=t.imageSize.width,this.height=t.imageSize.height),t.position&&(this.position=t.position),t.scale&&(this.scale=t.scale),this.repaintAllLayers(),this.trigger("snapshotLoad"),this.trigger("drawingChange",{})}},t.prototype.loadSnapshotJSON=function(t){return console.warn("lc.loadSnapshotJSON() is deprecated. use lc.loadSnapshot(JSON.parse(snapshot)) instead."),this.loadSnapshot(JSON.parse(t))},t}()},{"../tools/Pencil":24,"./actions":3,"./bindEvents":4,"./canvasRenderer":5,"./math":10,"./renderSnapshotToImage":11,"./renderSnapshotToSVG":12,"./shapes":13,"./svgRenderer":14,"./util":15}],2:[function(t,e,n){var i,r,o,s;t("./fontmetrics.js"),s=function(t){var e,n,i,r,o,s,a,h;for(n=t.split(" "),i=0,o=0,s=n.length;s>o;o++)r=n[o],a=parseInt(r.replace("px",""),10),isNaN(a)||(i=a);if(!i)throw"Font size not found";return h=t.substring(n[0].length+1).replace("bold ","").replace("italic ","").replace("underline ",""),e=h,{fontSize:i,fontFamily:e}},o=function(t,e,n){var i,r,o,s,a,h,u,c,l,p;if(!e.length)return["",""];for(r=0,h=0,u=0,p=!1;;)if(r+=1,o=r>=e.length,a=!o&&e[r].match(/\s/),s=a||o,l=e.substring(0,r),i=n?t.measureTextWidth(l).width<=n:!0,i&&(u=r),s&&p&&(p=!1,i&&(h=r)),p=!a,o||!i){if(i)return[e,""];if(h>0){for(c=h+1;c<e.length&&e[c].match("/s/");)c+=1;return[e.substring(0,h),e.substring(c)]}return[e.substring(0,u),e.substring(u)]}},r=function(t,e,n){var i,r,s,a,h,u,c,l,p;for(p=e.split(/\r\n|\r|\n/g),s=[],i=0,r=p.length;r>i;i++)if(l=p[i],h=o(t,l,n),a=h[0],c=h[1],a)for(;a;)s.push(a),u=o(t,c,n),a=u[0],c=u[1];else s.push(l);return s},i=function(){function t(t,e,n,i,o){var a,h,u;this.text=e,this.font=n,this.forcedWidth=i,this.forcedHeight=o,u=s(this.font),a=u.fontFamily,h=u.fontSize,t.font=this.font,t.textBaseline="baseline",this.emDashWidth=t.measureTextWidth("—",h,a).width,this.caratWidth=t.measureTextWidth("|",h,a).width,this.lines=r(t,this.text,this.forcedWidth),this.metricses=this.lines.map(function(e){return function(n){return t.measureText2(n||"X",h,e.font)}}(this)),this.metrics={ascent:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.ascent})),descent:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.descent})),fontsize:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.fontsize})),leading:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.leading})),width:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.width})),height:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.height})),bounds:{minx:Math.min.apply(Math,this.metricses.map(function(t){var e;return e=t.bounds,e.minx})),miny:Math.min.apply(Math,this.metricses.map(function(t){var e;return e=t.bounds,e.miny})),maxx:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.bounds,e.maxx})),maxy:Math.max.apply(Math,this.metricses.map(function(t){var e;return e=t.bounds,e.maxy}))}},this.boundingBoxWidth=Math.ceil(this.metrics.width)}return t.prototype.draw=function(t,e,n){var i,r,o,s,a,h;for(t.textBaseline="top",t.font=this.font,i=0,a=this.lines,h=[],r=0,o=a.length;o>r;r++)s=a[r],t.fillText(s,e,n+i*this.metrics.leading),h.push(i+=1);return h},t.prototype.getWidth=function(t){return null==t&&(t=!1),this.forcedWidth?this.forcedWidth:t?this.metrics.bounds.maxx+this.caratWidth:this.metrics.bounds.maxx},t.prototype.getHeight=function(){return this.forcedHeight||this.metrics.leading*this.lines.length},t}(),e.exports=i},{"./fontmetrics.js":7}],3:[function(t,e,n){var i,r;r=function(){function t(t,e,n){this.lc=t,this.oldShapes=e,this.newShapes=n}return t.prototype["do"]=function(){return this.lc.shapes=this.newShapes,this.lc.repaintLayer("main")},t.prototype.undo=function(){return this.lc.shapes=this.oldShapes,this.lc.repaintLayer("main")},t}(),i=function(){function t(t,e,n){this.lc=t,this.shape=e,this.previousShapeId=null!=n?n:null}return t.prototype["do"]=function(){var t,e,n,i,r,o;if(this.lc.shapes.length&&this.lc.shapes[this.lc.shapes.length-1].id!==this.previousShapeId&&null!==this.previousShapeId){for(i=[],t=!1,r=this.lc.shapes,e=0,n=r.length;n>e;e++)o=r[e],i.push(o),o.id===this.previousShapeId&&(i.push(this.shape),t=!0);t||i.push(this.shape),this.lc.shapes=i}else this.lc.shapes.push(this.shape);return this.lc.repaintLayer("main")},t.prototype.undo=function(){var t,e,n,i,r;if(this.lc.shapes[this.lc.shapes.length-1].id===this.shape.id)this.lc.shapes.pop();else{for(n=[],i=this.lc.shapes,t=0,e=i.length;e>t;t++)r=i[t],r.id!==this.shape.id&&n.push(r);lc.shapes=n}return this.lc.repaintLayer("main")},t}(),e.exports={ClearAction:r,AddShapeAction:i}},{}],4:[function(t,e,n){var i,r,o,s;o=function(t,e){var n,i,r;return i=e.changedTouches[0].clientX,r=e.changedTouches[0].clientY,n=t.getBoundingClientRect(),[i-n.left,r-n.top]},s=function(t,e){var n;return n=t.getBoundingClientRect(),{left:e.clientX-n.left,top:e.clientY-n.top}},r=function(t){return null!=t.buttons?1===t.buttons:t.which>0},e.exports=i=function(t,e,n){var i,r,a,h,u,c;return null==n&&(n=!1),c=[],r=function(n){return function(n){var i;return n.preventDefault(),i=s(e,n),t.pointerMove(i.left,i.top)}}(this),a=function(n){return function(n){var i;return n.preventDefault(),e.onselectstart=function(){return!0},i=s(e,n),t.pointerUp(i.left,i.top),document.removeEventListener("mousemove",r),document.removeEventListener("mouseup",a),e.addEventListener("mousemove",r)}}(this),e.addEventListener("mousedown",function(n){return function(n){var i,o;if("canvas"===n.target.tagName.toLowerCase())return i=!0,n.preventDefault(),e.onselectstart=function(){return!1},o=s(e,n),t.pointerDown(o.left,o.top),e.removeEventListener("mousemove",r),document.addEventListener("mousemove",r),document.addEventListener("mouseup",a)}}(this)),u=function(n){return n.preventDefault(),t.pointerMove.apply(t,o(e,n))},h=function(n){return n.preventDefault(),t.pointerUp.apply(t,o(e,n)),document.removeEventListener("touchmove",u),document.removeEventListener("touchend",h),document.removeEventListener("touchcancel",h)},e.addEventListener("touchstart",function(n){return"canvas"===n.target.tagName.toLowerCase()?(n.preventDefault(),1===n.touches.length?(t.pointerDown.apply(t,o(e,n)),document.addEventListener("touchmove",u),document.addEventListener("touchend",h),document.addEventListener("touchcancel",h)):t.pointerMove.apply(t,o(e,n))):void 0}),n&&(console.warn("Keyboard panning is deprecated."),i=function(e){switch(e.keyCode){case 37:t.pan(-10,0);break;case 38:t.pan(0,-10);break;case 39:t.pan(10,0);break;case 40:t.pan(0,10)}return t.repaintAllLayers()},document.addEventListener("keydown",i),c.push(function(){return document.removeEventListener(i)})),function(){var t,e,n,i;for(i=[],e=0,n=c.length;n>e;e++)t=c[e],i.push(t());return i}}},{}],5:[function(t,e,n){var i,r,o,s,a,h,u,c,l,p,d;u=t("./lineEndCapShapes"),d={},r=function(t,e,n){return d[t]={drawFunc:e,drawLatestFunc:n}},c=function(){},p=function(t,e,n){var i;if(null==n&&(n={}),null==n.shouldIgnoreUnsupportedShapes&&(n.shouldIgnoreUnsupportedShapes=!1),null==n.retryCallback&&(n.retryCallback=c),null==n.shouldOnlyDrawLatest&&(n.shouldOnlyDrawLatest=!1),null==n.bufferCtx&&(n.bufferCtx=null),i=n.bufferCtx,d[e.className])return n.shouldOnlyDrawLatest&&d[e.className].drawLatestFunc?d[e.className].drawLatestFunc(t,i,e,n.retryCallback):d[e.className].drawFunc(t,e,n.retryCallback);if(n.shouldIgnoreUnsupportedShapes)return console.warn("Can't render shape of type "+e.className+" to canvas");throw"Can't render shape of type "+e.className+" to canvas"},l=function(t,e,n){return p(t.getContext("2d"),e,n)},r("Rectangle",function(t,e){var n,i;return n=e.x,i=e.y,e.strokeWidth%2!==0&&(n+=.5,i+=.5),t.fillStyle=e.fillColor,t.fillRect(n,i,e.width,e.height),t.lineWidth=e.strokeWidth,t.strokeStyle=e.strokeColor,t.strokeRect(n,i,e.width,e.height)}),r("Ellipse",function(t,e){var n,i,r,o;return t.save(),o=Math.floor(e.width/2),r=Math.floor(e.height/2),n=e.x+o,i=e.y+r,t.translate(n,i),t.scale(1,Math.abs(e.height/e.width)),t.beginPath(),t.arc(0,0,Math.abs(o),0,2*Math.PI),t.closePath(),t.restore(),t.fillStyle=e.fillColor,t.fill(),t.lineWidth=e.strokeWidth,t.strokeStyle=e.strokeColor,t.stroke()}),r("SelectionBox",function(){var t;return t=function(t,e,n){var i,r;return i=e.x,r=e.y,0!==n?(t.fillStyle="#fff",t.fillRect(i,r,n,n),t.strokeStyle="#000",t.strokeRect(i,r,n,n)):void 0},function(e,n){return t(e,n.getTopLeftHandleRect(),n.handleSize),t(e,n.getTopRightHandleRect(),n.handleSize),t(e,n.getBottomLeftHandleRect(),n.handleSize),t(e,n.getBottomRightHandleRect(),n.handleSize),n.backgroundColor&&(e.fillStyle=n.backgroundColor,e.fillRect(n._br.x-n.margin,n._br.y-n.margin,n._br.width+2*n.margin,n._br.height+2*n.margin)),e.lineWidth=1,e.strokeStyle="#000",e.setLineDash([2,4]),e.strokeRect(n._br.x-n.margin,n._br.y-n.margin,n._br.width+2*n.margin,n._br.height+2*n.margin),e.setLineDash([])}}()),r("Image",function(t,e,n){return e.image.width?1===e.scale?t.drawImage(e.image,e.x,e.y):t.drawImage(e.image,e.x,e.y,e.image.width*e.scale,e.image.height*e.scale):n?e.image.onload=n:void 0}),r("Line",function(t,e){var n,i,r,o,s;if(e.x1!==e.x2||e.y1!==e.y2)return i=e.x1,r=e.x2,o=e.y1,s=e.y2,e.strokeWidth%2!==0&&(i+=.5,r+=.5,o+=.5,s+=.5),t.lineWidth=e.strokeWidth,t.strokeStyle=e.color,t.lineCap=e.capStyle,e.dash&&t.setLineDash(e.dash),t.beginPath(),t.moveTo(i,o),t.lineTo(r,s),t.stroke(),e.dash&&t.setLineDash([]),n=Math.max(2.2*e.strokeWidth,5),e.endCapShapes[0]&&u[e.endCapShapes[0]].drawToCanvas(t,i,o,Math.atan2(o-s,i-r),n,e.color),e.endCapShapes[1]?u[e.endCapShapes[1]].drawToCanvas(t,r,s,Math.atan2(s-o,r-i),n,e.color):void 0}),i=function(t,e,n,i){var r,o,s,a;if(null==n&&(n=!1),null==i&&(i="round"),e.length){for(t.lineCap=i,t.strokeStyle=e[0].color,t.lineWidth=e[0].size,t.beginPath(),e[0].size%2===0?t.moveTo(e[0].x,e[0].y):t.moveTo(e[0].x+.5,e[0].y+.5),a=e.slice(1),r=0,o=a.length;o>r;r++)s=a[r],e[0].size%2===0?t.lineTo(s.x,s.y):t.lineTo(s.x+.5,s.y+.5);return n?t.closePath():void 0}},a=function(t,e){return i(t,e.smoothedPoints),t.stroke()},h=function(t,e,n){var r,o,s;return n.tail?(s=n.smoothedPoints.length-n.segmentSize*n.tailSize,o=s<2*n.segmentSize?0:s,r=s+n.segmentSize+1,i(e,n.smoothedPoints.slice(o,r)),e.stroke()):(i(e,n.smoothedPoints),e.stroke())},r("LinePath",a,h),o=function(t,e){return t.save(),t.globalCompositeOperation="destination-out",a(t,e),t.restore()},s=function(t,e,n){return t.save(),t.globalCompositeOperation="destination-out",e.save(),e.globalCompositeOperation="destination-out",h(t,e,n),t.restore(),e.restore()},r("ErasedLinePath",o,s),r("Text",function(t,e){return e.renderer||e._makeRenderer(t),t.fillStyle=e.color,e.renderer.draw(t,e.x,e.y)}),r("Polygon",function(t,e){return t.fillStyle=e.fillColor,i(t,e.points,e.isClosed,"butt"),t.fill(),t.stroke()}),e.exports={defineCanvasRenderer:r,renderShapeToCanvas:l,renderShapeToContext:p}},{"./lineEndCapShapes":8}],6:[function(t,e,n){"use strict";e.exports={imageURLPrefix:"lib/img",primaryColor:"hsla(0, 0%, 0%, 1)",secondaryColor:"hsla(0, 0%, 100%, 1)",backgroundColor:"transparent",strokeWidths:[1,2,5,10,20,30],defaultStrokeWidth:5,toolbarPosition:"top",keyboardShortcuts:!1,imageSize:{width:"infinite",height:"infinite"},backgroundShapes:[],watermarkImage:null,watermarkScale:1,zoomMin:.2,zoomMax:4,zoomStep:.2,snapshot:null,onInit:function(){},tools:[t("../tools/Pencil"),t("../tools/Eraser"),t("../tools/Line"),t("../tools/Rectangle"),t("../tools/Ellipse"),t("../tools/Text"),t("../tools/Polygon"),t("../tools/Pan"),t("../tools/Eyedropper")]}},{"../tools/Ellipse":19,"../tools/Eraser":20,"../tools/Eyedropper":21,"../tools/Line":22,"../tools/Pan":23,"../tools/Pencil":24,"../tools/Polygon":25,"../tools/Rectangle":26,"../tools/Text":28}],7:[function(t,e,n){"use strict";!function(){if(!document.defaultView.getComputedStyle)throw"ERROR: 'document.defaultView.getComputedStyle' not found. This library only works in browsers that can report computed CSS values.";CanvasRenderingContext2D.prototype.measureTextWidth=CanvasRenderingContext2D.prototype.measureText;var t=function(t,e){return document.defaultView.getComputedStyle(t,null).getPropertyValue(e)};CanvasRenderingContext2D.prototype.measureText2=function(e,n,i){var r=this.measureTextWidth(e),o=!/\S/.test(e);r.fontsize=n;var s=document.createElement("div");s.style.position="absolute",s.style.opacity=0,s.style.font=i,s.innerHTML=e+"<br/>"+e,document.body.appendChild(s),r.leading=1.2*n;var a=t(s,"height");if(a=a.replace("px",""),a>=2*n&&(r.leading=a/2|0),document.body.removeChild(s),o)r.ascent=0,r.descent=0,r.bounds={minx:0,maxx:r.width,miny:0,maxy:0},r.height=0;else{var h=document.createElement("canvas"),u=100;h.width=r.width+u,h.height=3*n,h.style.opacity=1,h.style.font=i;var c=h.getContext("2d");c.font=i;var l=h.width,p=h.height,d=p/2;c.fillStyle="white",c.fillRect(-1,-1,l+2,p+2),c.fillStyle="black",c.fillText(e,u/2,d);for(var f=c.getImageData(0,0,l,p).data,g=0,y=4*l,m=f.length;++g<m&&255===f[g];);var S=g/y|0;for(g=m-1;--g>0&&255===f[g];);var x=g/y|0;for(g=0;m>g&&255===f[g];)g+=y,g>=m&&(g=g-m+4);var v=g%y/4|0,w=1;for(g=m-3;g>=0&&255===f[g];)g-=y,0>g&&(g=m-3-4*w++);var b=g%y/4+1|0;r.ascent=d-S,r.descent=x-d,r.bounds={minx:v-u/2,maxx:b-u/2,miny:0,maxy:x-S},r.height=1+(x-S)}return r}}()},{}],8:[function(t,e,n){e.exports={arrow:function(){var t;return t=function(t,e,n,i,r){return[{x:t+Math.cos(n+Math.PI/2)*i/2,y:e+Math.sin(n+Math.PI/2)*i/2},{x:t+Math.cos(n)*r,y:e+Math.sin(n)*r},{x:t+Math.cos(n-Math.PI/2)*i/2,y:e+Math.sin(n-Math.PI/2)*i/2}]},{drawToCanvas:function(e,n,i,r,o,s,a){var h;return null==a&&(a=0),a=a||o,e.fillStyle=s,e.lineWidth=0,e.strokeStyle="transparent",e.beginPath(),h=t(n,i,r,o,a),e.moveTo(h[0].x,h[0].y),e.lineTo(h[1].x,h[1].y),e.lineTo(h[2].x,h[2].y),e.fill()},svg:function(e,n,i,r,o,s){var a;return null==s&&(s=0),s=s||r,a=t(e,n,i,r,s),"<polygon fill='"+o+"' stroke='none' points='"+a.map(function(t){return t.x+","+t.y})+"' />"}}}()}},{}],9:[function(t,e,n){var i,r,o;o={},r=function(t){return o=t},i=function(t){var e;return e=o[t],e||t},e.exports={localize:r,_:i}},{}],10:[function(t,e,n){var i,r,o,s,a,h;i=t("./shapes").Point,h=t("./util"),o={},o.toPoly=function(t){var e,n,i,o,a,h,u;for(h=[],u=[],n=0,e=0,i=t.length;i>e;e++)a=t[e],o=s(a,r(t,n)),h=h.concat([o[0]]),u=[o[1]].concat(u),n+=1;return h.concat(u)},r=function(t,e){var n;return t.length<3&&(n={x:0,y:0}),n=0===e?r(t,e+1):e===t.length-1?r(t,e-1):o.diff(t[e-1],t[e+1])},o.diff=function(t,e){return{x:e.x-t.x,y:e.y-t.y}},a=function(t){var e;return e=o.len(t),{x:t.x/e,y:t.y/e}},s=function(t,e){return e=a(e),e.x=e.x*t.size/2,e.y=e.y*t.size/2,[{x:t.x-e.y,y:t.y+e.x,color:t.color},{x:t.x+e.y,y:t.y-e.x,color:t.color}]},o.len=function(t){return Math.sqrt(Math.pow(t.x,2)+Math.pow(t.y,2))},o.scalePositionScalar=function(t,e,n,i){var r,o;return o=e*n,r=e*i,t+(o-r)/2},e.exports=o},{"./shapes":13,"./util":15}],11:[function(t,e,n){var i,r,o,s;s=t("./util"),r=t("./shapes").JSONToShape,i="infinite",o=function(t,e,n){return e.width?(t.save(),t.translate(t.canvas.width/2,t.canvas.height/2),t.scale(n,n),t.drawImage(e,-e.width/2,-e.height/2),t.restore()):void 0},e.exports=function(t,e){var n,a,h,u,c,l,p,d;return null==e&&(e={}),null==e.scale&&(e.scale=1),l=function(){var e,n,i,o;for(i=t.shapes,o=[],e=0,n=i.length;n>e;e++)c=i[e],o.push(r(c));return o}(),a=[],t.backgroundShapes&&(a=function(){var e,n,i,o;for(i=t.backgroundShapes,o=[],e=0,n=i.length;n>e;e++)c=i[e],o.push(r(c));return o}()),null==e.margin&&(e.margin={top:0,right:0,bottom:0,left:0}),u=t.imageSize||{width:i,height:i},h=t.colors||{background:"transparent"},n=l.concat(a),p=document.createElement("canvas"),d=p.getContext("2d"),e.rect?(e.rect.x-=e.margin.left,e.rect.y-=e.margin.top,e.rect.width+=e.margin.left+e.margin.right,e.rect.height+=e.margin.top+e.margin.bottom):e.rect=s.getDefaultImageRect(function(){var t,e,i;for(i=[],t=0,e=n.length;e>t;t++)c=n[t],i.push(c.getBoundingRect(d));return i}(),u,e.margin),p.width=e.rect.width*e.scale,p.height=e.rect.height*e.scale,d.fillStyle=h.background,d.fillRect(0,0,p.width,p.height),e.rect.width&&e.rect.height?(e.watermarkImage&&o(d,e.watermarkImage,e.watermarkScale),s.combineCanvases(p,s.renderShapes(a,e.rect,e.scale),s.renderShapes(l,e.rect,e.scale))):null}},{"./shapes":13,"./util":15}],12:[function(t,e,n){var i,r,o;o=t("./util"),r=t("./shapes").JSONToShape,i="infinite",e.exports=function(t,e){var n,s,a,h,u,c,l,p;return null==e&&(e={}),p=function(){var e,n,i,o;for(i=t.shapes,o=[],e=0,n=i.length;n>e;e++)l=i[e],o.push(r(l));return o}(),s=[],t.backgroundShapes&&(s=function(){var e,n,i,o;for(i=t.backgroundShapes,o=[],e=0,n=i.length;n>e;e++)l=i[e],o.push(r(l));return o}()),null==e.margin&&(e.margin={top:0,right:0,bottom:0,left:0}),c=t.imageSize||{width:i,height:i},a=t.colors||{background:"transparent"},n=p.concat(s),u=document.createElement("canvas"),h=u.getContext("2d"),e.rect?(e.rect.x-=e.margin.left,e.rect.y-=e.margin.top,e.rect.width+=e.margin.left+e.margin.right,e.rect.height+=e.margin.top+e.margin.bottom):e.rect=o.getDefaultImageRect(function(){var t,e,i;for(i=[],t=0,e=n.length;e>t;t++)l=n[t],
i.push(l.getBoundingRect(h));return i}(),c,e.margin),LC.renderShapesToSVG(s.concat(p),e.rect,a.background)}},{"./shapes":13,"./util":15}],13:[function(t,e,n){var i,r,o,s,a,h,u,c,l,p,d,f,g,y,m,S,x,v,w,b,k,C;C=t("./util"),o=t("./TextRenderer"),y=t("./lineEndCapShapes"),S=t("./canvasRenderer"),d=S.defineCanvasRenderer,v=S.renderShapeToContext,x=t("./svgRenderer"),f=x.defineSVGRenderer,w=x.renderShapeToSVG,k={},g=function(t,e){var n,i,r,o,s,a,h,u;n=function(t,n,i,r,o,s,a,h,u,c,l,p,d,f,g,y){return e.constructor.call(this,t,n,i,r,o,s,a,h,u,c,l,p,d,f,g,y),this},n.prototype.className=t,n.fromJSON=e.fromJSON,e.draw&&(s=e.draw,a=e.draw||function(t,e,n){return this.draw(t,e,n)},i=function(t,e,n){return s.call(e,t,n)},r=function(t,e,n,i){return a.call(n,t,e,i)},delete e.draw,e.drawLatest&&delete e.drawLatest,d(t,i,r)),e.toSVG&&(h=e.toSVG,u=function(t){return h.call(t)},delete e.toSVG,f(t,u)),n.prototype.draw=function(t,e){return v(t,this,{retryCallback:e})},n.prototype.drawLatest=function(t,e,n){return v(t,this,{retryCallback:n,bufferCtx:e,shouldOnlyDrawLatest:!0})},n.prototype.toSVG=function(){return w(this)};for(o in e)"fromJSON"!==o&&(n.prototype[o]=e[o]);return k[t]=n,n},p=function(t,e,n,i,r,o,s,a,h,u,c,l,p,d,f,g,y){var m;return m=new k[t](e,n,i,r,o,s,a,h,u,c,l,p,d,f,g,y),m.id=C.getGUID(),m},i=function(t){var e,n,i,r;return e=t.className,n=t.data,i=t.id,e in k?(r=k[e].fromJSON(n),r?(i&&(r.id=i),r):(console.log("Unreadable shape:",e,n),null)):(console.log("Unknown shape:",e,n),null)},b=function(t){return{className:t.className,data:t.toJSON(),id:t.id}},l=function(t,e){return e?l(h(h(c(t))),e-1):t},c=function(t){var e,n,i,r,o;for(t=[t[0]].concat(t).concat(C.last(t)),o=[],e=0,r=0,n=t.length;n>r;r++)i=t[r],o[2*e]=i,t[e+1]&&(o[2*e+1]=u(i,t[e+1])),e+=1;return o},h=function(t){var e,n,i,r,o;for(e=[],n=0,o=0,i=t.length;i>o;o++)r=t[o],t[n+1]&&(e[n]=u(r,t[n+1])),n+=1;return e},u=function(t,e){return p("Point",{x:t.x+(e.x-t.x)/2,y:t.y+(e.y-t.y)/2,size:t.size+(e.size-t.size)/2,color:t.color})},g("Image",{constructor:function(t){return null==t&&(t={}),this.x=t.x||0,this.y=t.y||0,this.scale=t.scale||1,this.image=t.image||null},getBoundingRect:function(){return{x:this.x,y:this.y,width:this.image.width*this.scale,height:this.image.height*this.scale}},toJSON:function(){return{x:this.x,y:this.y,imageSrc:this.image.src,imageObject:this.image,scale:this.scale}},fromJSON:function(t){var e,n;return e=null,(null!=(n=t.imageObject)?n.width:void 0)?e=t.imageObject:(e=new Image,e.src=t.imageSrc),p("Image",{x:t.x,y:t.y,image:e,scale:t.scale})},move:function(t){return null==t&&(t={}),this.x=this.x-t.xDiff,this.y=this.y-t.yDiff},setUpperLeft:function(t){return null==t&&(t={}),this.x=t.x,this.y=t.y}}),g("Rectangle",{constructor:function(t){return null==t&&(t={}),this.x=t.x||0,this.y=t.y||0,this.width=t.width||0,this.height=t.height||0,this.strokeWidth=t.strokeWidth||1,this.strokeColor=t.strokeColor||"black",this.fillColor=t.fillColor||"transparent"},getBoundingRect:function(){return{x:this.x-this.strokeWidth/2,y:this.y-this.strokeWidth/2,width:this.width+this.strokeWidth,height:this.height+this.strokeWidth}},toJSON:function(){return{x:this.x,y:this.y,width:this.width,height:this.height,strokeWidth:this.strokeWidth,strokeColor:this.strokeColor,fillColor:this.fillColor}},fromJSON:function(t){return p("Rectangle",t)},move:function(t){return null==t&&(t={}),this.x=this.x-t.xDiff,this.y=this.y-t.yDiff},setUpperLeft:function(t){return null==t&&(t={}),this.x=t.x,this.y=t.y}}),g("Ellipse",{constructor:function(t){return null==t&&(t={}),this.x=t.x||0,this.y=t.y||0,this.width=t.width||0,this.height=t.height||0,this.strokeWidth=t.strokeWidth||1,this.strokeColor=t.strokeColor||"black",this.fillColor=t.fillColor||"transparent"},getBoundingRect:function(){return{x:this.x-this.strokeWidth/2,y:this.y-this.strokeWidth/2,width:this.width+this.strokeWidth,height:this.height+this.strokeWidth}},toJSON:function(){return{x:this.x,y:this.y,width:this.width,height:this.height,strokeWidth:this.strokeWidth,strokeColor:this.strokeColor,fillColor:this.fillColor}},fromJSON:function(t){return p("Ellipse",t)},move:function(t){return null==t&&(t={}),this.x=this.x-t.xDiff,this.y=this.y-t.yDiff},setUpperLeft:function(t){return null==t&&(t={}),this.x=t.x,this.y=t.y}}),g("Line",{constructor:function(t){return null==t&&(t={}),this.x1=t.x1||0,this.y1=t.y1||0,this.x2=t.x2||0,this.y2=t.y2||0,this.strokeWidth=t.strokeWidth||1,this.color=t.color||"black",this.capStyle=t.capStyle||"round",this.endCapShapes=t.endCapShapes||[null,null],this.dash=t.dash||null},getBoundingRect:function(){return{x:Math.min(this.x1,this.x2)-this.strokeWidth/2,y:Math.min(this.y1,this.y2)-this.strokeWidth/2,width:Math.abs(this.x2-this.x1)+this.strokeWidth/2,height:Math.abs(this.y2-this.y1)+this.strokeWidth/2}},toJSON:function(){return{x1:this.x1,y1:this.y1,x2:this.x2,y2:this.y2,strokeWidth:this.strokeWidth,color:this.color,capStyle:this.capStyle,dash:this.dash,endCapShapes:this.endCapShapes}},fromJSON:function(t){return p("Line",t)},move:function(t){return null==t&&(t={}),this.x1=this.x1-t.xDiff,this.y1=this.y1-t.yDiff,this.x2=this.x2-t.xDiff,this.y2=this.y2-t.yDiff},setUpperLeft:function(t){var e,n,i;return null==t&&(t={}),e=this.getBoundingRect(),n=e.x-t.x,i=e.y-t.y,this.move({xDiff:n,yDiff:i})}}),a=function(t){var e,n,i,r,o;if(!t.length)return!1;for(o=t[0].size,e=t[0].color,r=0,n=t.length;n>r;r++)if(i=t[r],i.size===o&&i.color===e||console.log(o,e,i.size,i.color),i.size!==o||i.color!==e)return!1;return!0},s=function(t,e){var n,r,o,s,a;return r=null,e.points?r=function(){var t,r,o,s;for(o=e.points,s=[],r=0,t=o.length;t>r;r++)n=o[r],s.push(i(n));return s}():e.pointCoordinatePairs&&(r=function(){var t,n,r,o,h;for(r=e.pointCoordinatePairs,h=[],n=0,t=r.length;t>n;n++)o=r[n],s=o[0],a=o[1],h.push(i({className:"Point",data:{x:s,y:a,size:e.pointSize,color:e.pointColor,smooth:e.smooth}}));return h}()),o=null,e.smoothedPointCoordinatePairs&&(o=function(){var t,n,r,o,h;for(r=e.smoothedPointCoordinatePairs,h=[],n=0,t=r.length;t>n;n++)o=r[n],s=o[0],a=o[1],h.push(i({className:"Point",data:{x:s,y:a,size:e.pointSize,color:e.pointColor,smooth:e.smooth}}));return h}()),r[0]?p(t,{points:r,smoothedPoints:o,order:e.order,tailSize:e.tailSize,smooth:e.smooth}):null},m={constructor:function(t){var e,n,i,r,o;if(null==t&&(t={}),i=t.points||[],this.order=t.order||3,this.tailSize=t.tailSize||3,this.smooth="smooth"in t?t.smooth:!0,this.segmentSize=Math.pow(2,this.order),this.sampleSize=this.tailSize+1,t.smoothedPoints)return this.points=t.points,this.smoothedPoints=t.smoothedPoints;for(this.points=[],o=[],r=0,e=i.length;e>r;r++)n=i[r],o.push(this.addPoint(n));return o},getBoundingRect:function(){return C.getBoundingRect(this.points.map(function(t){return{x:t.x-t.size/2,y:t.y-t.size/2,width:t.size,height:t.size}}))},toJSON:function(){var t,e;return a(this.points)?{order:this.order,tailSize:this.tailSize,smooth:this.smooth,pointCoordinatePairs:function(){var t,n,i,r;for(i=this.points,r=[],n=0,t=i.length;t>n;n++)e=i[n],r.push([e.x,e.y]);return r}.call(this),smoothedPointCoordinatePairs:function(){var t,n,i,r;for(i=this.smoothedPoints,r=[],n=0,t=i.length;t>n;n++)e=i[n],r.push([e.x,e.y]);return r}.call(this),pointSize:this.points[0].size,pointColor:this.points[0].color}:{order:this.order,tailSize:this.tailSize,smooth:this.smooth,points:function(){var e,n,i,r;for(i=this.points,r=[],n=0,e=i.length;e>n;n++)t=i[n],r.push(b(t));return r}.call(this)}},fromJSON:function(t){return s("LinePath",t)},addPoint:function(t){return this.points.push(t),this.smooth?!this.smoothedPoints||this.points.length<this.sampleSize?this.smoothedPoints=l(this.points,this.order):(this.tail=C.last(l(C.last(this.points,this.sampleSize),this.order),this.segmentSize*this.tailSize),this.smoothedPoints=this.smoothedPoints.slice(0,this.smoothedPoints.length-this.segmentSize*(this.tailSize-1)).concat(this.tail)):void(this.smoothedPoints=this.points)},move:function(t){var e,n,i,r;for(null==t&&(t={}),i=this.smooth?this.smoothedPoints:this.points,r=0,e=i.length;e>r;r++)n=i[r],n.move(t);return this.points=this.smoothedPoints},setUpperLeft:function(t){var e,n,i;return null==t&&(t={}),e=this.getBoundingRect(),n=e.x-t.x,i=e.y-t.y,this.move({xDiff:n,yDiff:i})}},r=g("LinePath",m),g("ErasedLinePath",{constructor:m.constructor,toJSON:m.toJSON,addPoint:m.addPoint,getBoundingRect:m.getBoundingRect,fromJSON:function(t){return s("ErasedLinePath",t)}}),g("Point",{constructor:function(t){return null==t&&(t={}),this.x=t.x||0,this.y=t.y||0,this.size=t.size||0,this.color=t.color||""},getBoundingRect:function(){return{x:this.x-this.size/2,y:this.y-this.size/2,width:this.size,height:this.size}},toJSON:function(){return{x:this.x,y:this.y,size:this.size,color:this.color}},fromJSON:function(t){return p("Point",t)},move:function(t){return null==t&&(t={}),this.x=this.x-t.xDiff,this.y=this.y-t.yDiff},setUpperLeft:function(t){return null==t&&(t={}),this.x=t.x,this.y=t.y}}),g("Polygon",{constructor:function(t){var e,n,i,r,o;for(null==t&&(t={}),this.points=t.points,this.fillColor=t.fillColor||"white",this.strokeColor=t.strokeColor||"black",this.strokeWidth=t.strokeWidth,this.dash=t.dash||null,null==t.isClosed&&(t.isClosed=!0),this.isClosed=t.isClosed,r=this.points,o=[],i=0,e=r.length;e>i;i++)n=r[i],n.color=this.strokeColor,o.push(n.size=this.strokeWidth);return o},addPoint:function(t,e){return this.points.push(LC.createShape("Point",{x:t,y:e}))},getBoundingRect:function(){return C.getBoundingRect(this.points.map(function(t){return t.getBoundingRect()}))},toJSON:function(){return{strokeWidth:this.strokeWidth,fillColor:this.fillColor,strokeColor:this.strokeColor,dash:this.dash,isClosed:this.isClosed,pointCoordinatePairs:this.points.map(function(t){return[t.x,t.y]})}},fromJSON:function(t){return t.points=t.pointCoordinatePairs.map(function(e){var n,i;return n=e[0],i=e[1],p("Point",{x:n,y:i,size:t.strokeWidth,color:t.strokeColor})}),p("Polygon",t)},move:function(t){var e,n,i,r,o;for(null==t&&(t={}),r=this.points,o=[],i=0,e=r.length;e>i;i++)n=r[i],o.push(n.move(t));return o},setUpperLeft:function(t){var e,n,i;return null==t&&(t={}),e=this.getBoundingRect(),n=e.x-t.x,i=e.y-t.y,this.move({xDiff:n,yDiff:i})}}),g("Text",{constructor:function(t){return null==t&&(t={}),this.x=t.x||0,this.y=t.y||0,this.v=t.v||0,this.text=t.text||"",this.color=t.color||"black",this.font=t.font||"18px sans-serif",this.forcedWidth=t.forcedWidth||null,this.forcedHeight=t.forcedHeight||null},_makeRenderer:function(t){return t.lineHeight=1.2,this.renderer=new o(t,this.text,this.font,this.forcedWidth,this.forcedHeight),this.v<1?(console.log("repairing baseline"),this.v=1,this.x-=this.renderer.metrics.bounds.minx,this.y-=this.renderer.metrics.leading-this.renderer.metrics.descent):void 0},setText:function(t){return this.text=t,this.renderer=null},setFont:function(t){return this.font=t,this.renderer=null},setPosition:function(t,e){return this.x=t,this.y=e},setSize:function(t,e){return this.forcedWidth=Math.max(t,0),this.forcedHeight=Math.max(e,0),this.renderer=null},enforceMaxBoundingRect:function(t){var e,n,i;return e=this.getBoundingRect(t.ctx),i={x:-t.position.x/t.scale,y:-t.position.y/t.scale,width:t.canvas.width/t.scale,height:t.canvas.height/t.scale},e.x+e.width>i.x+i.width?(n=e.x-i.x,this.forcedWidth=i.width-n-10,this.renderer=null):void 0},getBoundingRect:function(t,e){if(null==e&&(e=!1),!this.renderer){if(!t)throw"Must pass ctx if text hasn't been rendered yet";this._makeRenderer(t)}return{x:Math.floor(this.x),y:Math.floor(this.y),width:Math.ceil(this.renderer.getWidth(!0)),height:Math.ceil(this.renderer.getHeight())}},toJSON:function(){return{x:this.x,y:this.y,text:this.text,color:this.color,font:this.font,forcedWidth:this.forcedWidth,forcedHeight:this.forcedHeight,v:this.v}},fromJSON:function(t){return p("Text",t)},move:function(t){return null==t&&(t={}),this.x=this.x-t.xDiff,this.y=this.y-t.yDiff},setUpperLeft:function(t){return null==t&&(t={}),this.x=t.x,this.y=t.y}}),g("SelectionBox",{constructor:function(t){return null==t&&(t={}),this.shape=t.shape,null!=t.handleSize?this.handleSize=t.handleSize:this.handleSize=10,this.margin=4,this.backgroundColor=t.backgroundColor||null,this._br=this.shape.getBoundingRect(t.ctx)},toJSON:function(){return{shape:b(this.shape),backgroundColor:this.backgroundColor}},fromJSON:function(t){var e,n,r,o;return o=t.shape,n=t.handleSize,r=t.margin,e=t.backgroundColor,p("SelectionBox",{shape:i(o),backgroundColor:e})},getTopLeftHandleRect:function(){return{x:this._br.x-this.handleSize-this.margin,y:this._br.y-this.handleSize-this.margin,width:this.handleSize,height:this.handleSize}},getBottomLeftHandleRect:function(){return{x:this._br.x-this.handleSize-this.margin,y:this._br.y+this._br.height+this.margin,width:this.handleSize,height:this.handleSize}},getTopRightHandleRect:function(){return{x:this._br.x+this._br.width+this.margin,y:this._br.y-this.handleSize-this.margin,width:this.handleSize,height:this.handleSize}},getBottomRightHandleRect:function(){return{x:this._br.x+this._br.width+this.margin,y:this._br.y+this._br.height+this.margin,width:this.handleSize,height:this.handleSize}},getBoundingRect:function(){return{x:this._br.x-this.margin,y:this._br.y-this.margin,width:this._br.width+2*this.margin,height:this._br.height+2*this.margin}}}),e.exports={defineShape:g,createShape:p,JSONToShape:i,shapeToJSON:b}},{"./TextRenderer":2,"./canvasRenderer":5,"./lineEndCapShapes":8,"./svgRenderer":14,"./util":15}],14:[function(t,e,n){var i,r,o,s;r=t("./lineEndCapShapes"),s={},i=function(t,e){return s[t]=e},o=function(t,e){if(null==e&&(e={}),null==e.shouldIgnoreUnsupportedShapes&&(e.shouldIgnoreUnsupportedShapes=!1),s[t.className])return s[t.className](t);if(e.shouldIgnoreUnsupportedShapes)return console.warn("Can't render shape of type "+t.className+" to SVG"),"";throw"Can't render shape of type "+t.className+" to SVG"},i("Rectangle",function(t){var e,n,i,r,o,s,a,h;return r=t.x,a=t.y,o=t.x+t.width,h=t.y+t.height,i=Math.min(r,o),s=Math.min(a,h),n=Math.max(r,o)-i,e=Math.max(a,h)-s,t.strokeWidth%2!==0&&(i+=.5,s+=.5),"<rect x='"+i+"' y='"+s+"' width='"+n+"' height='"+e+"' stroke='"+t.strokeColor+"' fill='"+t.fillColor+"' stroke-width='"+t.strokeWidth+"' />"}),i("SelectionBox",function(t){return""}),i("Ellipse",function(t){var e,n,i,r;return r=Math.floor(t.width/2),i=Math.floor(t.height/2),e=t.x+r,n=t.y+i,"<ellipse cx='"+e+"' cy='"+n+"' rx='"+Math.abs(r)+"' ry='"+Math.abs(i)+"' stroke='"+t.strokeColor+"' fill='"+t.fillColor+"' stroke-width='"+t.strokeWidth+"' />"}),i("Image",function(t){return"<image x='"+t.x+"' y='"+t.y+"' width='"+t.image.naturalWidth*t.scale+"' height='"+t.image.naturalHeight*t.scale+"' xlink:href='"+t.image.src+"' />"}),i("Line",function(t){var e,n,i,o,s,a,h;return i=t.dash?"stroke-dasharray='"+t.dash.join(", ")+"'":"",n="",e=Math.max(2.2*t.strokeWidth,5),o=t.x1,s=t.x2,a=t.y1,h=t.y2,t.strokeWidth%2!==0&&(o+=.5,s+=.5,a+=.5,h+=.5),t.endCapShapes[0]&&(n+=r[t.endCapShapes[0]].svg(o,a,Math.atan2(a-h,o-s),e,t.color)),t.endCapShapes[1]&&(n+=r[t.endCapShapes[1]].svg(s,h,Math.atan2(h-a,s-o),e,t.color)),"<g> <line x1='"+o+"' y1='"+a+"' x2='"+s+"' y2='"+h+"' "+i+" stroke-linecap='"+t.capStyle+"' stroke='"+t.color+" 'stroke-width='"+t.strokeWidth+"' /> "+n+" </g>"}),i("LinePath",function(t){return"<polyline fill='none' points='"+t.smoothedPoints.map(function(t){var e;return e=t.strokeWidth%2===0?0:.5,t.x+e+","+(t.y+e)}).join(" ")+"' stroke='"+t.points[0].color+"' stroke-linecap='round' stroke-width='"+t.points[0].size+"' />"}),i("ErasedLinePath",function(t){return""}),i("Polygon",function(t){return t.isClosed?"<polygon fill='"+t.fillColor+"' points='"+t.points.map(function(t){var e;return e=t.strokeWidth%2===0?0:.5,t.x+e+","+(t.y+e)}).join(" ")+"' stroke='"+t.strokeColor+"' stroke-width='"+t.strokeWidth+"' />":"<polyline fill='"+t.fillColor+"' points='"+t.points.map(function(t){var e;return e=t.strokeWidth%2===0?0:.5,t.x+e+","+(t.y+e)}).join(" ")+"' stroke='none' /> <polyline fill='none' points='"+t.points.map(function(t){var e;return e=t.strokeWidth%2===0?0:.5,t.x+e+","+(t.y+e)}).join(" ")+"' stroke='"+t.strokeColor+"' stroke-width='"+t.strokeWidth+"' />"}),i("Text",function(t){var e,n,i;return i=t.forcedWidth?"width='"+t.forcedWidth+"px'":"",e=t.forcedHeight?"height='"+t.forcedHeight+"px'":"",n=t.text.split(/\r\n|\r|\n/g),t.renderer&&(n=t.renderer.lines),"<text x='"+t.x+"' y='"+t.y+"' "+i+" "+e+" fill='"+t.color+"' style='font: "+t.font+";'> "+n.map(function(e){return function(e,n){var i;return i=0===n?0:"1.2em","<tspan x='"+t.x+"' dy='"+i+"' alignment-baseline='text-before-edge'> "+e+" </tspan>"}}(this)).join("")+" </text>"}),e.exports={defineSVGRenderer:i,renderShapeToSVG:o}},{"./lineEndCapShapes":8}],15:[function(t,e,n){var i,r,o,s,a=[].slice;o=Array.prototype.slice,i=t("./canvasRenderer").renderShapeToContext,r=t("./svgRenderer").renderShapeToSVG,s={addImageOnload:function(t,e){var n;return n=t.onload,t.onload=function(){return"function"==typeof n&&n(),e()},t},last:function(t,e){return null==e&&(e=null),e?o.call(t,Math.max(t.length-e,0)):t[t.length-1]},classSet:function(t){var e,n;e=[];for(n in t)t[n]&&e.push(n);return e.join(" ")},matchElementSize:function(t,e,n,i){var r;return null==i&&(i=function(){}),r=function(r){return function(){var r,o,s;for(o=0,s=e.length;s>o;o++)r=e[o],r.style.width=t.offsetWidth+"px",r.style.height=t.offsetHeight+"px",null!=r.width&&(r.setAttribute("width",r.offsetWidth*n),r.setAttribute("height",r.offsetHeight*n));return i()}}(this),t.addEventListener("resize",r),window.addEventListener("resize",r),window.addEventListener("orientationchange",r),r(),r},combineCanvases:function(){var t,e,n,i,r,o,s,h;for(n=1<=arguments.length?a.call(arguments,0):[],t=document.createElement("canvas"),t.width=n[0].width,t.height=n[0].height,r=0,s=n.length;s>r;r++)e=n[r],t.width=Math.max(e.width,t.width),t.height=Math.max(e.height,t.height);for(i=t.getContext("2d"),o=0,h=n.length;h>o;o++)e=n[o],i.drawImage(e,0,0);return t},renderShapes:function(t,e,n,r){var o,s,a,h;for(null==n&&(n=1),null==r&&(r=null),r=r||document.createElement("canvas"),r.width=e.width*n,r.height=e.height*n,o=r.getContext("2d"),o.translate(-e.x*n,-e.y*n),o.scale(n,n),s=0,a=t.length;a>s;s++)h=t[s],i(o,h);return r},renderShapesToSVG:function(t,e,n){var i,o,s,a;return s=e.x,a=e.y,o=e.width,i=e.height,("<svg xmlns='http://www.w3.org/2000/svg' width='"+o+"' height='"+i+"' viewBox='0 0 "+o+" "+i+"'> <rect width='"+o+"' height='"+i+"' x='0' y='0' fill='"+n+"' /> <g transform='translate("+-s+", "+-a+")'> "+t.map(r).join("")+" </g> </svg>").replace(/(\r\n|\n|\r)/gm,"")},getBoundingRect:function(t,e,n){var i,r,o,s,a,h,u;if(!t.length)return{x:0,y:0,width:e,height:n};for(a=t[0].x,h=t[0].y,o=t[0].x+t[0].width,s=t[0].y+t[0].height,i=0,r=t.length;r>i;i++)u=t[i],a=Math.floor(Math.min(u.x,a)),h=Math.floor(Math.min(u.y,h)),o=Math.ceil(Math.max(o,u.x+u.width)),s=Math.ceil(Math.max(s,u.y+u.height));return a=e?0:a,h=n?0:h,o=e||o,s=n||s,{x:a,y:h,width:o-a,height:s-h}},getDefaultImageRect:function(t,e,n){var i,r,o;return null==e&&(e={width:0,height:0}),null==n&&(n={top:0,right:0,bottom:0,left:0}),o=e.width,i=e.height,r=s.getBoundingRect(t,"infinite"===o?0:o,"infinite"===i?0:i),r.x-=n.left,r.y-=n.top,r.width+=n.left+n.right,r.height+=n.top+n.bottom,r},getBackingScale:function(t){return null==window.devicePixelRatio?1:window.devicePixelRatio>1?window.devicePixelRatio:1},requestAnimationFrame:(window.requestAnimationFrame||window.setTimeout).bind(window),getGUID:function(){var t;return t=function(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)},function(){return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}}(),requestAnimationFrame:function(t){return window.requestAnimationFrame?window.requestAnimationFrame(t):window.webkitRequestAnimationFrame?window.webkitRequestAnimationFrame(t):window.mozRequestAnimationFrame?window.mozRequestAnimationFrame(t):setTimeout(t,0)},cancelAnimationFrame:function(t){return window.cancelAnimationFrame?window.cancelAnimationFrame(t):window.webkitCancelRequestAnimationFrame?window.webkitCancelRequestAnimationFrame(t):window.webkitCancelAnimationFrame?window.webkitCancelAnimationFrame(t):window.mozCancelAnimationFrame?window.mozCancelAnimationFrame(t):clearTimeout(t)}},e.exports=s},{"./canvasRenderer":5,"./svgRenderer":14}],16:[function(t,e,n){"use strict";!function(){function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}t.prototype=window.CustomEvent.prototype,window.CustomEvent=t}()},{}],17:[function(t,e,n){"use strict";var i=!1;CanvasRenderingContext2D.prototype.setLineDash||(CanvasRenderingContext2D.prototype.setLineDash=function(){i||(console.warn("context2D.setLineDash is a no-op in this browser."),i=!0)}),e.exports=null},{}],18:[function(t,e,n){var i,r,o,s,a,h,u,c,l,p,d,f,g,y,m,S,x,v;t("./ie_customevent"),t("./ie_setLineDash"),i=t("./core/LiterallyCanvas"),h=t("./core/defaultOptions"),o=t("./core/canvasRenderer"),S=t("./core/svgRenderer"),m=t("./core/shapes"),v=t("./core/util"),f=t("./core/renderSnapshotToImage"),g=t("./core/renderSnapshotToSVG"),p=t("./core/localization").localize,s={snapshotToShapes:function(t){var e,n,i,r,o;for(i=t.shapes,r=[],e=0,n=i.length;n>e;e++)o=i[e],r.push(m.JSONToShape(o));return r},snapshotJSONToShapes:function(t){return s.snapshotToShapes(JSON.parse(t))}},r=t("./tools/base"),x={Pencil:t("./tools/Pencil"),Eraser:t("./tools/Eraser"),Line:t("./tools/Line"),Rectangle:t("./tools/Rectangle"),Ellipse:t("./tools/Ellipse"),Text:t("./tools/Text"),Polygon:t("./tools/Polygon"),Pan:t("./tools/Pan"),Eyedropper:t("./tools/Eyedropper"),SelectShape:t("./tools/SelectShape"),Tool:r.Tool,ToolWithStroke:r.ToolWithStroke},u=h.tools,a=h.imageURLPrefix,y=function(t){return a=t,h.imageURLPrefix=t},c=function(t,e){var n,i,r,o,s;null==e&&(e={});for(o in h)o in e||(e[o]=h[o]);for(s=t.children,i=0,r=s.length;r>i;i++)n=s[i],t.removeChild(n);return l(t,e)},l=function(t,e){var n,r,o;return o=t.className,-1===[" "," "].join(t.className).indexOf(" literally ")&&(t.className=t.className+" literally"),t.className=t.className+" toolbar-hidden",n=document.createElement("div"),n.className="lc-drawing",t.appendChild(n),r=new i(n,e),r.teardown=function(){var e,n,i,s;for(r._teardown(),s=t.children,n=0,i=s.length;i>n;n++)e=s[n],t.removeChild(e);return t.className=o},"onInit"in e&&e.onInit(r),r},d=function(t){return t.fn.literallycanvas=function(t){return null==t&&(t={}),this.each(function(e){return function(e,n){return n.literallycanvas=c(n,t)}}(this)),this}},"undefined"!=typeof window&&(window.LC={init:c},window.$&&d(window.$)),e.exports={init:c,registerJQueryPlugin:d,util:v,tools:x,setDefaultImageURLPrefix:y,defaultTools:u,defineShape:m.defineShape,createShape:m.createShape,JSONToShape:m.JSONToShape,shapeToJSON:m.shapeToJSON,defineCanvasRenderer:o.defineCanvasRenderer,renderShapeToContext:o.renderShapeToContext,renderShapeToCanvas:o.renderShapeToCanvas,renderShapesToCanvas:v.renderShapes,defineSVGRenderer:S.defineSVGRenderer,renderShapeToSVG:S.renderShapeToSVG,renderShapesToSVG:v.renderShapesToSVG,snapshotToShapes:s.snapshotToShapes,snapshotJSONToShapes:s.snapshotJSONToShapes,renderSnapshotToImage:f,renderSnapshotToSVG:g,localize:p}},{"./core/LiterallyCanvas":1,"./core/canvasRenderer":5,"./core/defaultOptions":6,"./core/localization":9,"./core/renderSnapshotToImage":11,"./core/renderSnapshotToSVG":12,"./core/shapes":13,"./core/svgRenderer":14,"./core/util":15,"./ie_customevent":16,"./ie_setLineDash":17,"./tools/Ellipse":19,"./tools/Eraser":20,"./tools/Eyedropper":21,"./tools/Line":22,"./tools/Pan":23,"./tools/Pencil":24,"./tools/Polygon":25,"./tools/Rectangle":26,"./tools/SelectShape":27,"./tools/Text":28,"./tools/base":29}],19:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").ToolWithStroke,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Ellipse",e.prototype.iconName="ellipse",e.prototype.begin=function(t,e,n){return this.currentShape=o("Ellipse",{x:t,y:e,strokeWidth:this.strokeWidth,strokeColor:n.getColor("primary"),fillColor:n.getColor("secondary")})},e.prototype["continue"]=function(t,e,n){return this.currentShape.width=t-this.currentShape.x,this.currentShape.height=e-this.currentShape.y,n.drawShapeInProgress(this.currentShape)},e.prototype.end=function(t,e,n){return n.saveShape(this.currentShape)},e}(r)},{"../core/shapes":13,"./base":29}],20:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./Pencil"),o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Eraser",e.prototype.iconName="eraser",e.prototype.makePoint=function(t,e,n){return o("Point",{x:t,y:e,size:this.strokeWidth,color:"#000"})},e.prototype.makeShape=function(){return o("ErasedLinePath")},e}(r)},{"../core/shapes":13,"./Pencil":24}],21:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").Tool,o=function(t,e){var n,i,r;return i=e.x,r=e.y,n=t.getImageData(i,r,1,1).data,n[3]?"rgb("+n[0]+", "+n[1]+", "+n[2]+")":null},e.exports=i=function(t){function e(t){e.__super__.constructor.call(this,t),this.strokeOrFill="stroke"}return s(e,t),e.prototype.name="Eyedropper",e.prototype.iconName="eyedropper",e.prototype.optionsStyle="stroke-or-fill",e.prototype.readColor=function(t,e,n){var i,r,s,a;return a=n.getDefaultImageRect(),i=n.getImage(),s=o(i.getContext("2d"),{x:t-a.x,y:e-a.y}),r=s||n.getColor("background"),"stroke"===this.strokeOrFill?n.setColor("primary",s):n.setColor("secondary",s)},e.prototype.begin=function(t,e,n){return this.readColor(t,e,n)},e.prototype["continue"]=function(t,e,n){return this.readColor(t,e,n)},e}(r)},{"./base":29}],22:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").ToolWithStroke,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Line",e.prototype.iconName="line",e.prototype.optionsStyle="line-options-and-stroke-width",e.prototype.begin=function(t,e,n){return this.currentShape=o("Line",{x1:t,y1:e,x2:t,y2:e,strokeWidth:this.strokeWidth,dash:function(){switch(!1){case!this.isDashed:return[2*this.strokeWidth,4*this.strokeWidth];default:return null}}.call(this),endCapShapes:this.hasEndArrow?[null,"arrow"]:null,color:n.getColor("primary")})},e.prototype["continue"]=function(t,e,n){return this.currentShape.x2=t,this.currentShape.y2=e,n.drawShapeInProgress(this.currentShape)},e.prototype.end=function(t,e,n){return n.saveShape(this.currentShape)},e}(r)},{"../core/shapes":13,"./base":29}],23:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").Tool,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Pan",e.prototype.iconName="pan",e.prototype.usesSimpleAPI=!1,e.prototype.didBecomeActive=function(t){var e;return e=[],this.unsubscribe=function(t){return function(){var t,n,i,r;for(r=[],n=0,i=e.length;i>n;n++)t=e[n],r.push(t());return r}}(this),e.push(t.on("lc-pointerdown",function(e){return function(n){var i,r;return i=n.rawX,r=n.rawY,e.oldPosition=t.position,e.pointerStart={x:i,y:r}}}(this))),e.push(t.on("lc-pointerdrag",function(e){return function(n){var i,r,o;return r=n.rawX,o=n.rawY,i={x:(r-e.pointerStart.x)*t.backingScale,y:(o-e.pointerStart.y)*t.backingScale},t.setPan(e.oldPosition.x+i.x,e.oldPosition.y+i.y)}}(this)))},e.prototype.willBecomeInactive=function(t){return this.unsubscribe()},e}(r)},{"../core/shapes":13,"./base":29}],24:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").ToolWithStroke,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Pencil",e.prototype.iconName="pencil",e.prototype.eventTimeThreshold=10,e.prototype.begin=function(t,e,n){return this.color=n.getColor("primary"),this.currentShape=this.makeShape(),this.currentShape.addPoint(this.makePoint(t,e,n)),this.lastEventTime=Date.now()},e.prototype["continue"]=function(t,e,n){var i;return i=Date.now()-this.lastEventTime,i>this.eventTimeThreshold?(this.lastEventTime+=i,this.currentShape.addPoint(this.makePoint(t,e,n)),n.drawShapeInProgress(this.currentShape)):void 0},e.prototype.end=function(t,e,n){return n.saveShape(this.currentShape),this.currentShape=void 0},e.prototype.makePoint=function(t,e,n){return o("Point",{x:t,y:e,size:this.strokeWidth,color:this.color})},e.prototype.makeShape=function(){return o("LinePath")},e}(r)},{"../core/shapes":13,"./base":29}],25:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").ToolWithStroke,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Polygon",e.prototype.iconName="polygon",e.prototype.usesSimpleAPI=!1,e.prototype.didBecomeActive=function(t){var n,i,r,o,s,a,h;return e.__super__.didBecomeActive.call(this,t),h=[],this.polygonUnsubscribe=function(t){return function(){var t,e,n,i;for(i=[],e=0,n=h.length;n>e;e++)t=h[e],i.push(t());return i}}(this),this.points=null,this.maybePoint=null,r=function(e){return function(){return e._getWillFinish()?e._close(t):(t.trigger("lc-polygon-started"),e.points?e.points.push(e.maybePoint):e.points=[e.maybePoint],e.maybePoint={x:e.maybePoint.x,y:e.maybePoint.y},t.setShapesInProgress(e._getShapes(t)),t.repaintLayer("main"))}}(this),i=function(e){return function(n){var i,r;return i=n.x,r=n.y,e.maybePoint?(e.maybePoint.x=i,e.maybePoint.y=r,t.setShapesInProgress(e._getShapes(t)),t.repaintLayer("main")):void 0}}(this),n=function(e){return function(n){var i,r;return i=n.x,r=n.y,e.maybePoint={x:i,y:r},t.setShapesInProgress(e._getShapes(t)),t.repaintLayer("main")}}(this),a=function(e){return function(){return e.maybePoint={x:1/0,y:1/0},e._close(t)}}(this),s=function(e){return function(){return e.maybePoint=e.points[0],e._close(t)}}(this),o=function(e){return function(){return e._cancel(t)}}(this),h.push(t.on("drawingChange",function(e){return function(){return e._cancel(t)}}(this))),h.push(t.on("lc-pointerdown",n)),h.push(t.on("lc-pointerdrag",i)),h.push(t.on("lc-pointermove",i)),h.push(t.on("lc-pointerup",r)),h.push(t.on("lc-polygon-finishopen",a)),h.push(t.on("lc-polygon-finishclosed",s)),h.push(t.on("lc-polygon-cancel",o))},e.prototype.willBecomeInactive=function(t){return e.__super__.willBecomeInactive.call(this,t),(this.points||this.maybePoint)&&this._cancel(t),this.polygonUnsubscribe()},e.prototype._getArePointsClose=function(t,e){return Math.abs(t.x-e.x)+Math.abs(t.y-e.y)<10},e.prototype._getWillClose=function(){return this.points&&this.points.length>1&&this.maybePoint?this._getArePointsClose(this.points[0],this.maybePoint):!1;
},e.prototype._getWillFinish=function(){return this.points&&this.points.length>1&&this.maybePoint?this._getArePointsClose(this.points[0],this.maybePoint)||this._getArePointsClose(this.points[this.points.length-1],this.maybePoint):!1},e.prototype._cancel=function(t){return t.trigger("lc-polygon-stopped"),this.maybePoint=null,this.points=null,t.setShapesInProgress([]),t.repaintLayer("main")},e.prototype._close=function(t){return t.trigger("lc-polygon-stopped"),t.setShapesInProgress([]),this.points.length>2&&t.saveShape(this._getShape(t,!1)),this.maybePoint=null,this.points=null},e.prototype._getShapes=function(t,e){var n;return null==e&&(e=!0),n=this._getShape(t,e),n?[n]:[]},e.prototype._getShape=function(t,e){var n;return null==e&&(e=!0),n=[],this.points&&(n=n.concat(this.points)),!e&&n.length<3?null:(e&&this.maybePoint&&n.push(this.maybePoint),n.length>1?o("Polygon",{isClosed:this._getWillClose(),strokeColor:t.getColor("primary"),fillColor:t.getColor("secondary"),strokeWidth:this.strokeWidth,points:n.map(function(t){return o("Point",t)})}):null)},e.prototype.optionsStyle="polygon-and-stroke-width",e}(r)},{"../core/shapes":13,"./base":29}],26:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").ToolWithStroke,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return s(e,t),e.prototype.name="Rectangle",e.prototype.iconName="rectangle",e.prototype.begin=function(t,e,n){return this.currentShape=o("Rectangle",{x:t,y:e,strokeWidth:this.strokeWidth,strokeColor:n.getColor("primary"),fillColor:n.getColor("secondary")})},e.prototype["continue"]=function(t,e,n){return this.currentShape.width=t-this.currentShape.x,this.currentShape.height=e-this.currentShape.y,n.drawShapeInProgress(this.currentShape)},e.prototype.end=function(t,e,n){return n.saveShape(this.currentShape)},e}(r)},{"../core/shapes":13,"./base":29}],27:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;r=t("./base").Tool,o=t("../core/shapes").createShape,e.exports=i=function(t){function e(t){this.selectCanvas=document.createElement("canvas"),this.selectCanvas.style["background-color"]="transparent",this.selectCtx=this.selectCanvas.getContext("2d")}return s(e,t),e.prototype.name="SelectShape",e.prototype.usesSimpleAPI=!1,e.prototype.didBecomeActive=function(t){var e,n,i,r;return r=[],this._selectShapeUnsubscribe=function(t){return function(){var t,e,n,i;for(i=[],e=0,n=r.length;n>e;e++)t=r[e],i.push(t());return i}}(this),e=function(e){return function(n){var i,r,s,a;return s=n.x,a=n.y,e.didDrag=!1,r=e._getPixel(s,a,t,e.selectCtx),e.selectedShape=t.shapes[r],null!=e.selectedShape?(t.trigger("shapeSelected",{selectedShape:e.selectedShape}),t.setShapesInProgress([e.selectedShape,o("SelectionBox",{shape:e.selectedShape,handleSize:0})]),t.repaintLayer("main"),i=e.selectedShape.getBoundingRect(),e.dragOffset={x:s-i.x,y:a-i.y}):void 0}}(this),n=function(e){return function(n){var i,r;return i=n.x,r=n.y,null!=e.selectedShape?(e.didDrag=!0,e.selectedShape.setUpperLeft({x:i-e.dragOffset.x,y:r-e.dragOffset.y}),t.setShapesInProgress([e.selectedShape,o("SelectionBox",{shape:e.selectedShape,handleSize:0})]),t.repaintLayer("main")):void 0}}(this),i=function(e){return function(n){var i,r;return i=n.x,r=n.y,e.didDrag?(e.didDrag=!1,t.trigger("shapeMoved",{shape:e.selectedShape}),t.trigger("drawingChange",{}),t.repaintLayer("main"),e._drawSelectCanvas(t)):void 0}}(this),r.push(t.on("lc-pointerdown",e)),r.push(t.on("lc-pointerdrag",n)),r.push(t.on("lc-pointerup",i)),this._drawSelectCanvas(t)},e.prototype.willBecomeInactive=function(t){return this._selectShapeUnsubscribe(),t.setShapesInProgress([])},e.prototype._drawSelectCanvas=function(t){var e;return this.selectCanvas.width=t.canvas.width,this.selectCanvas.height=t.canvas.height,this.selectCtx.clearRect(0,0,this.selectCanvas.width,this.selectCanvas.height),e=t.shapes.map(function(t){return function(e,n){return o("SelectionBox",{shape:e,handleSize:0,backgroundColor:"#"+t._intToHex(n)})}}(this)),t.draw(e,this.selectCtx)},e.prototype._intToHex=function(t){return("000000"+t.toString(16)).slice(-6)},e.prototype._getPixel=function(t,e,n,i){var r,o;return r=n.drawingCoordsToClientCoords(t,e),o=i.getImageData(r.x,r.y,1,1).data,o[3]?parseInt(this._rgbToHex(o[0],o[1],o[2]),16):null},e.prototype._componentToHex=function(t){var e;return e=t.toString(16),("0"+e).slice(-2)},e.prototype._rgbToHex=function(t,e,n){return""+this._componentToHex(t)+this._componentToHex(e)+this._componentToHex(n)},e}(r)},{"../core/shapes":13,"./base":29}],28:[function(t,e,n){var i,r,o,s,a=function(t,e){function n(){this.constructor=t}for(var i in e)h.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},h={}.hasOwnProperty;r=t("./base").Tool,o=t("../core/shapes").createShape,s=function(t,e){return t.x<e.x?!1:t.y<e.y?!1:t.x>e.x+e.width?!1:!(t.y>e.y+e.height)},e.exports=i=function(t){function e(){this.text="",this.font="bold 18px sans-serif",this.currentShape=null,this.currentShapeState=null,this.initialShapeBoundingRect=null,this.dragAction=null,this.didDrag=!1}return a(e,t),e.prototype.name="Text",e.prototype.iconName="text",e.prototype.didBecomeActive=function(t){var e,n,i;return n=[],this.unsubscribe=function(t){return function(){var t,e,i,r;for(r=[],e=0,i=n.length;i>e;e++)t=n[e],r.push(t());return r}}(this),e=function(e){return function(){return e._ensureNotEditing(t),e._clearCurrentShape(t),t.repaintLayer("main")}}(this),i=function(e){return function(){return e._updateInputEl(t)}}(this),n.push(t.on("drawingChange",e)),n.push(t.on("zoom",i)),n.push(t.on("imageSizeChange",i)),n.push(t.on("snapshotLoad",function(e){return function(){return e._clearCurrentShape(t),t.repaintLayer("main")}}(this))),n.push(t.on("primaryColorChange",function(e){return function(n){return e.currentShape?(e.currentShape.color=n,e._updateInputEl(t),t.repaintLayer("main")):void 0}}(this))),n.push(t.on("setFont",function(e){return function(n){return e.currentShape?(e.font=n,e.currentShape.setFont(n),e._setShapesInProgress(t),e._updateInputEl(t),t.repaintLayer("main")):void 0}}(this)))},e.prototype.willBecomeInactive=function(t){return this.currentShape&&(this._ensureNotEditing(t),this.commit(t)),this.unsubscribe()},e.prototype.setText=function(t){return this.text=t},e.prototype._ensureNotEditing=function(t){return"editing"===this.currentShapeState?this._exitEditingState(t):void 0},e.prototype._clearCurrentShape=function(t){return this.currentShape=null,this.initialShapeBoundingRect=null,this.currentShapeState=null,t.setShapesInProgress([])},e.prototype.commit=function(t){return this.currentShape.text&&t.saveShape(this.currentShape),this._clearCurrentShape(t),t.repaintLayer("main")},e.prototype._getSelectionShape=function(t,e){return null==e&&(e=null),o("SelectionBox",{shape:this.currentShape,ctx:t,backgroundColor:e})},e.prototype._setShapesInProgress=function(t){switch(this.currentShapeState){case"selected":return t.setShapesInProgress([this._getSelectionShape(t.ctx),this.currentShape]);case"editing":return t.setShapesInProgress([this._getSelectionShape(t.ctx,"#fff")]);default:return t.setShapesInProgress([this.currentShape])}},e.prototype.begin=function(t,e,n){var i,r,a,h;return this.dragAction="none",this.didDrag=!1,"selected"===this.currentShapeState||"editing"===this.currentShapeState?(i=this.currentShape.getBoundingRect(n.ctx),h=this._getSelectionShape(n.ctx),a=h.getBoundingRect(),r={x:t,y:e},s(r,i)&&(this.dragAction="move"),s(r,h.getBottomRightHandleRect())&&(this.dragAction="resizeBottomRight"),s(r,h.getTopLeftHandleRect())&&(this.dragAction="resizeTopLeft"),s(r,h.getBottomLeftHandleRect())&&(this.dragAction="resizeBottomLeft"),s(r,h.getTopRightHandleRect())&&(this.dragAction="resizeTopRight"),"none"===this.dragAction&&"editing"===this.currentShapeState&&(this.dragAction="stop-editing",this._exitEditingState(n))):(this.color=n.getColor("primary"),this.currentShape=o("Text",{x:t,y:e,text:this.text,color:this.color,font:this.font,v:1}),this.dragAction="place",this.currentShapeState="selected"),"none"===this.dragAction?void this.commit(n):(this.initialShapeBoundingRect=this.currentShape.getBoundingRect(n.ctx),this.dragOffset={x:t-this.initialShapeBoundingRect.x,y:e-this.initialShapeBoundingRect.y},this._setShapesInProgress(n),n.repaintLayer("main"))},e.prototype["continue"]=function(t,e,n){var i,r,o;if("none"!==this.dragAction){switch(i=this.initialShapeBoundingRect,o=i.x+i.width,r=i.y+i.height,this.dragAction){case"place":this.currentShape.x=t,this.currentShape.y=e,this.didDrag=!0;break;case"move":this.currentShape.x=t-this.dragOffset.x,this.currentShape.y=e-this.dragOffset.y,this.didDrag=!0;break;case"resizeBottomRight":this.currentShape.setSize(t-(this.dragOffset.x-this.initialShapeBoundingRect.width)-i.x,e-(this.dragOffset.y-this.initialShapeBoundingRect.height)-i.y);break;case"resizeTopLeft":this.currentShape.setSize(o-t+this.dragOffset.x,r-e+this.dragOffset.y),this.currentShape.setPosition(t-this.dragOffset.x,e-this.dragOffset.y);break;case"resizeBottomLeft":this.currentShape.setSize(o-t+this.dragOffset.x,e-(this.dragOffset.y-this.initialShapeBoundingRect.height)-i.y),this.currentShape.setPosition(t-this.dragOffset.x,this.currentShape.y);break;case"resizeTopRight":this.currentShape.setSize(t-(this.dragOffset.x-this.initialShapeBoundingRect.width)-i.x,r-e+this.dragOffset.y),this.currentShape.setPosition(this.currentShape.x,e-this.dragOffset.y)}return this._setShapesInProgress(n),n.repaintLayer("main"),this._updateInputEl(n)}},e.prototype.end=function(t,e,n){return this.currentShape?(this.currentShape.setSize(this.currentShape.forcedWidth,0),"selected"===this.currentShapeState&&("place"===this.dragAction||"move"===this.dragAction&&!this.didDrag)&&this._enterEditingState(n),this._setShapesInProgress(n),n.repaintLayer("main"),this._updateInputEl(n)):void 0},e.prototype._enterEditingState=function(t){var e;if(this.currentShapeState="editing",this.inputEl)throw"State error";return this.inputEl=document.createElement("textarea"),this.inputEl.className="text-tool-input",this.inputEl.style.position="absolute",this.inputEl.style.transformOrigin="0px 0px",this.inputEl.style.backgroundColor="transparent",this.inputEl.style.border="none",this.inputEl.style.outline="none",this.inputEl.style.margin="0",this.inputEl.style.padding="4px",this.inputEl.style.zIndex="1000",this.inputEl.style.overflow="hidden",this.inputEl.style.resize="none",this.inputEl.value=this.currentShape.text,this.inputEl.addEventListener("mousedown",function(t){return t.stopPropagation()}),this.inputEl.addEventListener("touchstart",function(t){return t.stopPropagation()}),e=function(e){return function(n){return e.currentShape.setText(n.target.value),e.currentShape.enforceMaxBoundingRect(t),e._setShapesInProgress(t),t.repaintLayer("main"),e._updateInputEl(t),n.stopPropagation()}}(this),this.inputEl.addEventListener("keydown",function(e){return function(){return e._updateInputEl(t,!0)}}(this)),this.inputEl.addEventListener("keyup",e),this.inputEl.addEventListener("change",e),this._updateInputEl(t),t.containerEl.appendChild(this.inputEl),this.inputEl.focus(),this._setShapesInProgress(t)},e.prototype._exitEditingState=function(t){return this.currentShapeState="selected",t.containerEl.removeChild(this.inputEl),this.inputEl=null,this._setShapesInProgress(t),t.repaintLayer("main")},e.prototype._updateInputEl=function(t,e){var n,i;return null==e&&(e=!1),this.inputEl?(n=this.currentShape.getBoundingRect(t.ctx,!0),this.inputEl.style.font=this.currentShape.font,this.inputEl.style.color=this.currentShape.color,this.inputEl.style.left=t.position.x/t.backingScale+n.x*t.scale-4+"px",this.inputEl.style.top=t.position.y/t.backingScale+n.y*t.scale-4+"px",e&&!this.currentShape.forcedWidth?this.inputEl.style.width=n.width+10+this.currentShape.renderer.emDashWidth+"px":this.inputEl.style.width=n.width+12+"px",e?this.inputEl.style.height=n.height+10+this.currentShape.renderer.metrics.leading+"px":this.inputEl.style.height=n.height+10+"px",i="scale("+t.scale+")",this.inputEl.style.transform=i,this.inputEl.style.webkitTransform=i,this.inputEl.style.MozTransform=i,this.inputEl.style.msTransform=i,this.inputEl.style.OTransform=i):void 0},e.prototype.optionsStyle="font",e}(r)},{"../core/shapes":13,"./base":29}],29:[function(t,e,n){var i,r,o,s=function(t,e){function n(){this.constructor=t}for(var i in e)a.call(e,i)&&(t[i]=e[i]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},a={}.hasOwnProperty;o={},o.Tool=i=function(){function t(){}return t.prototype.name=null,t.prototype.iconName=null,t.prototype.usesSimpleAPI=!0,t.prototype.begin=function(t,e,n){},t.prototype["continue"]=function(t,e,n){},t.prototype.end=function(t,e,n){},t.prototype.optionsStyle=null,t.prototype.didBecomeActive=function(t){},t.prototype.willBecomeInactive=function(t){},t}(),o.ToolWithStroke=r=function(t){function e(t){this.strokeWidth=t.opts.defaultStrokeWidth}return s(e,t),e.prototype.optionsStyle="stroke-width",e.prototype.didBecomeActive=function(t){var e;return e=[],this.unsubscribe=function(t){return function(){var t,n,i,r;for(r=[],n=0,i=e.length;i>n;n++)t=e[n],r.push(t());return r}}(this),e.push(t.on("setStrokeWidth",function(e){return function(n){return e.strokeWidth=n,t.trigger("toolDidUpdateOptions")}}(this)))},e.prototype.willBecomeInactive=function(t){return this.unsubscribe()},e}(i),e.exports=o},{}]},{},[18])(18)});
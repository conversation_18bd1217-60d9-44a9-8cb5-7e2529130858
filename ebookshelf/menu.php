﻿<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] == 1 ) {
		require_once 'inc/check.user.inc.php';
	} else {
		require_once 'inc/check.student.inc.php';
	}
}

?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<title>卓思 EbookShelf</title>
	<script src="js/jquery.min.1.7.js"></script>
	<style>
html, body {
	padding: 0;
	margin: 0;
	user-select: none;
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
	background-color: rgb(255, 255, 204);
}
table {
	margin: auto;
	width: 90%;
	margin-top: 20px;
}
	</style>
</head>
<body>
	<table border="0">
		<tr>
			<th><a href="menu/小學音樂之旅電子書(V2.8)平台互動功能操作簡介.pdf" ><img src="menu/001.jpg" height="200"></a></th>
			<th><a href="menu/初中音樂新編(中文版)電子書(V2.8)互動功能操作簡介.pdf" ><img src="menu/002.jpg" height="200"></a></th>
			<th><a href="menu/初中音樂新編(英文版)電子書(V2.8)互動功能操作簡介.pdf" ><img src="menu/003.jpg" height="200"></a></th>
		</tr>
		<tr>
			<th><a href="menu/小學音樂之旅電子書(V2.8)平台互動功能操作簡介.pdf" >小學音樂之旅電子書(V2.8)平台互動功能操作簡介</a></th>
			<th><a href="menu/初中音樂新編(中文版)電子書(V2.8)互動功能操作簡介.pdf" >初中音樂新編(中文版)電子書(V2.8)互動功能操作簡介</a></th>
			<th><a href="menu/初中音樂新編(英文版)電子書(V2.8)互動功能操作簡介.pdf" >初中音樂新編(英文版)電子書(V2.8)互動功能操作簡介</a></th>
		</tr>
	</table>
	
	


</body>
</html>


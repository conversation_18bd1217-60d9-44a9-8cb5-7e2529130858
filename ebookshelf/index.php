﻿<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] == 1 ) {
		require_once 'inc/check.user.inc.php';
	} else {
		require_once 'inc/check.student.inc.php';
	}
}

?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<title>卓思 EbookShelf</title>
	<script src="js/jquery.min.1.7.js"></script>
	<style>
html, body {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	user-select: none;
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}
header {
	width: 100%;
	height: 60px;
	padding: 0;
	margin: 0;
	background-image: url(img/header.jpg);
	background-repeat: repeat-x;
	background-position: center; 
}
#header-menu {
	width:100%;
	height:100%;
	margin: 0 auto;
	border-collapse: collapse;
}
#header-menu td{
	vertical-align:middle;
	text-align:center;
	height:100%;
}
main {
	width: 100%;
	height: calc(100% - 80px); 
	padding: 0;
	margin: 0;
}
.book-cover {
	width:180px;
	height:180px;
	padding:10px;
	display:inline-block;
	text-align:center;
	vertical-align:middle;
}
.book-cover:hover {
	background-color:#FFE4E1;
}
.book-cover img{
	width:100%;
	height:100%;
	object-fit:contain;
}
.book-disabled {
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	-ms-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}

.sbx-meetup {
  display: inline-block;
  position: relative;
  width: 300px;
  height: 45px;
  white-space: nowrap;
  box-sizing: border-box;
  font-size: 16px;
}

.sbx-meetup__wrapper {
  width: 100%;
  height: 100%;
}

.sbx-meetup__input {
  display: inline-block;
  -webkit-transition: box-shadow .4s ease, background .4s ease;
  transition: box-shadow .4s ease, background .4s ease;
  border: 0;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px #444444;
  background: #F5F8FA;
  padding: 0;
  padding-right: 66px;
  padding-left: 12px;
  width: 100%;
  height: 100%;
  vertical-align: middle;
  white-space: normal;
  font-size: inherit;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.sbx-meetup__input::-webkit-search-decoration, .sbx-meetup__input::-webkit-search-cancel-button, .sbx-meetup__input::-webkit-search-results-button, .sbx-meetup__input::-webkit-search-results-decoration {
  display: none;
}

.sbx-meetup__input:hover {
  box-shadow: inset 0 0 0 1px #2b2b2b;
}

.sbx-meetup__input:focus, .sbx-meetup__input:active {
  outline: 0;
  box-shadow: inset 0 0 0 1px #000000;
  background: #FFFFFF;
}

.sbx-meetup__input::-webkit-input-placeholder {
  color: #BBBBBB;
}

.sbx-meetup__input::-moz-placeholder {
  color: #BBBBBB;
}

.sbx-meetup__input:-ms-input-placeholder {
  color: #BBBBBB;
}

.sbx-meetup__input::placeholder {
  color: #BBBBBB;
}

.sbx-meetup__submit {
  position: absolute;
  top: 0;
  right: 0;
  left: inherit;
  margin: 0;
  border: 0;
  border-radius: 0 3px 3px 0;
  background-color: rgba(255, 255, 255, 0);
  padding: 0;
  width: 45px;
  height: 100%;
  vertical-align: middle;
  text-align: center;
  font-size: inherit;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.sbx-meetup__submit::before {
  display: inline-block;
  margin-right: -4px;
  height: 100%;
  vertical-align: middle;
  content: '';
}

.sbx-meetup__submit:hover, .sbx-meetup__submit:active {
  cursor: pointer;
}

.sbx-meetup__submit:focus {
  outline: 0;
}

.sbx-meetup__submit svg {
  width: 25px;
  height: 25px;
  vertical-align: middle;
  fill: #505050;
}

.sbx-meetup__reset {
  display: none;
  position: absolute;
  top: 12px;
  right: 45px;
  margin: 0;
  border: 0;
  background: none;
  cursor: pointer;
  padding: 0;
  font-size: inherit;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  fill: rgba(0, 0, 0, 0.5);
}

.sbx-meetup__reset:focus {
  outline: 0;
}

.sbx-meetup__reset svg {
  display: block;
  margin: 4px;
  width: 13px;
  height: 13px;
}

.sbx-meetup__input:valid ~ .sbx-meetup__reset {
  display: block;
  -webkit-animation-name: sbx-reset-in;
          animation-name: sbx-reset-in;
  -webkit-animation-duration: .15s;
          animation-duration: .15s;
}

@-webkit-keyframes sbx-reset-in {
  0% {
    -webkit-transform: translate3d(-20%, 0, 0);
            transform: translate3d(-20%, 0, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
            transform: none;
    opacity: 1;
  }
}

@keyframes sbx-reset-in {
  0% {
    -webkit-transform: translate3d(-20%, 0, 0);
            transform: translate3d(-20%, 0, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
            transform: none;
    opacity: 1;
  }
}

#logout:link {
	color: black;
	text-decoration: none;
}
#logout:visited {
	color: black;
	text-decoration: none;
}
#logout:hover {
	color: orange;
	text-decoration: none;
}
#logout:active {
	color: black;
	text-decoration: none;
}

	</style>
	<script>
$( document ).ready(function() {
	
	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	
	console.log( "ready!" );

	$(".book-cover a").click(function( event ) {
		event.preventDefault();
		var dataTarget = $(this).attr("data-target");
		console.log(dataTarget);
		
		window.location = dataTarget + "/";
	});
	
	$("#searchReset").click(function( event ) {
		// event.preventDefault();
		$("#searchtext").focus();
		$('.book-cover').show();
	});
	
	$( ".searchbox" ).submit(function( event ) {
		event.preventDefault();
		
		var searchText = $("#searchtext").val();
		searchText = searchText.trim();
		searchText = searchText.toLowerCase();
		
		if ( searchText == "" ) {
			$('.book-cover').show();
		} else {
			$( ".book-cover" ).each(function( index ) {
				var dataSearch = $(this).attr("data-search");
				dataSearch = dataSearch.toLowerCase();
				// console.log( dataSearch );
				
				if ( dataSearch.includes(searchText) ) {
					$(this).show();
				} else {
					$(this).hide();
				}
				
			});
		}
		
	});
});
	
	</script>
</head>
<body>
	<header>
		<table id="header-menu">
			<tr>
				<td width="220"><a href="http://www.excellence.com.hk/" target="_blank" ><img src="img/logo.png" height="43" width="199"></td>
				<td>
					<form novalidate="novalidate" onsubmit="return false;" class="searchbox sbx-meetup">
					  <div role="search" class="sbx-meetup__wrapper">
						<input id="searchtext" type="search" name="search" placeholder="Search your books" autocomplete="off" required="required" class="sbx-meetup__input">
						<button type="submit" title="Submit your search query." class="sbx-meetup__submit">
						  <svg role="img" aria-label="Search">
							<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#sbx-icon-search-13"></use>
						  </svg>
						</button>
						<button id="searchReset" type="reset" title="Clear the search query." class="sbx-meetup__reset">
						  <svg role="img" aria-label="Reset">
							<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#sbx-icon-clear-3"></use>
						  </svg>
						</button>
					  </div>
					</form>

				
				</td>
				<td width="50">
					<a href="menu.php" target="_blank"><img style="vertical-align:bottom;" src="menu/menu.png" width="50"></a>
				</td>
				<td width="200">
					你好！<?php echo $user_name; ?>。<br>
					<a id="logout" href="logout.php">登出</a>
				</td>
			</tr>
		</table>
	</header>
	<main>
	
<?php

if ( $_SESSION['type'] == 1 ) {
	echo '<div class="book-cover" data-search="class" ><a href="#" data-target="book/class" ><img src="cover/classicon.jpg" title="class"></a></div>';
}

foreach ( $_SESSION['booklist'] as $booklist ) {
	echo '<div class="book-cover" data-search="'.$booklist[1].'" ><a href="#" data-target="'.$booklist[2].'" ><img src="cover/'.$booklist[3].'" title="'.$booklist[1].'"></a></div>';
}

?>
		
	</main>
	
	
<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol xmlns="http://www.w3.org/2000/svg" id="sbx-icon-search-13" viewBox="0 0 40 40">
    <path d="M26.804 29.01c-2.832 2.34-6.465 3.746-10.426 3.746C7.333 32.756 0 25.424 0 16.378 0 7.333 7.333 0 16.378 0c9.046 0 16.378 7.333 16.378 16.378 0 3.96-1.406 7.594-3.746 10.426l10.534 10.534c.607.607.61 1.59-.004 2.202-.61.61-1.597.61-2.202.004L26.804 29.01zm-10.426.627c7.323 0 13.26-5.936 13.26-13.26 0-7.32-5.937-13.257-13.26-13.257C9.056 3.12 3.12 9.056 3.12 16.378c0 7.323 5.936 13.26 13.258 13.26z"
    fill-rule="evenodd" />
  </symbol>
  <symbol xmlns="http://www.w3.org/2000/svg" id="sbx-icon-clear-3" viewBox="0 0 20 20">
    <path d="M8.114 10L.944 2.83 0 1.885 1.886 0l.943.943L10 8.113l7.17-7.17.944-.943L20 1.886l-.943.943-7.17 7.17 7.17 7.17.943.944L18.114 20l-.943-.943-7.17-7.17-7.17 7.17-.944.943L0 18.114l.943-.943L8.113 10z" fill-rule="evenodd" />
  </symbol>
</svg>


</body>
</html>
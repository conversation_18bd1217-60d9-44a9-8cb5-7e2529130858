# macOS 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# macOS 应用程序文件
*.app

# Xcode (如果使用 Xcode 编辑器)
*.xcodeproj
*.xcworkspace
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
xcuserdata/
build/
DerivedData/

# PHP 相关
# Composer 依赖
vendor/
composer.lock
composer.phar

# PHP 临时文件
*.tmp
*.temp
*.cache

# 配置文件 (包含敏感信息)
config.php
settings.local.php
database.config.php
.env
.env.local
.env.production

# 数据库文件
*.sql.backup
*.sql.bak
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/
error.log
access.log
php_errors.log

# 缓存目录
cache/
tmp/
temp/
.cache/

# 上传文件目录 (根据项目结构调整)
uploads/
files/
user_uploads/
ebookshelf/uploads/
ebookshelf/files/
ebookshelf/user_files/

# 会话文件
sessions/
*.sess

# IDE 和编辑器文件
# Visual Studio Code
.vscode/
*.code-workspace

# PhpStorm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# 备份文件
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 图片缓存 (如果有自动生成的缩略图等)
thumbs/
thumbnails/
.thumbnails/

# 开发工具
# Node.js (如果使用前端构建工具)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Bower
bower_components/

# 测试覆盖率报告
coverage/
*.coverage
.nyc_output/

# 性能分析文件
*.prof

# 本地开发服务器文件
.htaccess.local
.htpasswd

# 错误报告
error_reporting.txt
debug.txt

# 项目特定文件
# 根据您的项目结构，可能需要忽略的文件
ebookshelf/inc/settings.inc.php.local
ebookshelf/config/local.php

# 用户生成的内容
ebookshelf/cover/user_*
ebookshelf/pics/user_*
ebookshelf/pdf/user_*

# 临时文件
*.tmp.php
*.temp.php

# 安全相关
*.key
*.pem
*.crt
*.csr
private/
secrets/

# 文档生成
docs/build/
documentation/build/

# 其他
.sass-cache/
*.css.map
*.js.map
*.sql
